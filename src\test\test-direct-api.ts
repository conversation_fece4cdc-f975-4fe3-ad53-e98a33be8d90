/**
 * Direct API test to generate sentence and save to Firestore
 */

import axios from 'axios';
import * as admin from 'firebase-admin';

// Initialize Firebase Admin
if (!admin.apps.length) {
  admin.initializeApp();
}

const db = admin.firestore();

async function generateSentenceWithGemini() {
  console.log('🤖 Generating sentence with Gemini AI...\n');

  try {
    const apiKey = 'AIzaSyCyyWndJbQRD-ktpM7PYPktEzAo_6trjQs';
    
    const prompt = `Generate a simple Spanish sentence for daily conversation with Korean translation. Respond only in JSON format:
{"sentence": "Spanish sentence", "translation": "Korean translation"}

Example: {"sentence": "¿Dónde está el baño?", "translation": "화장실이 어디에요?"}`;
    
    const response = await axios.post(
      `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`,
      {
        contents: [{
          parts: [{
            text: prompt
          }]
        }]
      },
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    );

    if (response.data.candidates && response.data.candidates[0]) {
      const text = response.data.candidates[0].content.parts[0].text;
      console.log('✅ Gemini AI Response:', text);
      
      // Parse JSON
      const cleanedText = text.trim().replace(/```json\n?|\n?```/g, '');
      const parsed = JSON.parse(cleanedText);
      
      console.log('📝 Parsed Sentence:', parsed.sentence);
      console.log('🇰🇷 Parsed Translation:', parsed.translation);
      
      return parsed;
    } else {
      throw new Error('No response from Gemini AI');
    }

  } catch (error: any) {
    console.error('❌ Gemini AI API failed:', error.response?.data?.error?.message || error.message);
    throw error;
  }
}

async function searchYouTubeVideo(sentence: string) {
  console.log('\n📺 Searching YouTube video...\n');

  try {
    const apiKey = 'AIzaSyD8iQgRRyIRrG8ZGzAzgWWiNmQHccqegCg';
    const searchQuery = `${sentence} español conversación aprender spanish learning`;
    
    const response = await axios.get('https://www.googleapis.com/youtube/v3/search', {
      params: {
        key: apiKey,
        part: 'snippet',
        q: searchQuery,
        type: 'video',
        maxResults: 3,
        order: 'relevance',
        regionCode: 'KR',
        relevanceLanguage: 'es',
        videoDefinition: 'any',
        videoDuration: 'medium',
        safeSearch: 'strict'
      }
    });

    if (response.data.items && response.data.items.length > 0) {
      const video = response.data.items[0];
      const videoData = {
        videoUrl: `https://www.youtube.com/watch?v=${video.id.videoId}`,
        videoThumbnail: video.snippet.thumbnails.medium?.url || video.snippet.thumbnails.default?.url
      };
      
      console.log('✅ Found YouTube video:', video.snippet.title);
      console.log('🔗 URL:', videoData.videoUrl);
      console.log('🖼️  Thumbnail:', videoData.videoThumbnail);
      
      return videoData;
    } else {
      console.log('❌ No YouTube videos found');
      return null;
    }

  } catch (error: any) {
    console.error('❌ YouTube API failed:', error.response?.data || error.message);
    return null;
  }
}

async function saveToFirestore(sentence: string, translation: string, videoData: any) {
  console.log('\n💾 Saving to Firestore...\n');

  try {
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
    
    const dailySentenceData = {
      sentence,
      translation,
      date: today,
      ...(videoData && {
        videoUrl: videoData.videoUrl,
        videoThumbnail: videoData.videoThumbnail
      })
    };

    const docRef = db.collection('daily_sentences').doc(today);
    await docRef.set(dailySentenceData);

    console.log('✅ Successfully saved to Firestore!');
    console.log('📄 Document ID:', today);
    console.log('📝 Data:', JSON.stringify(dailySentenceData, null, 2));
    
    return dailySentenceData;

  } catch (error) {
    console.error('❌ Firestore save failed:', error);
    throw error;
  }
}

async function runFullTest() {
  console.log('🚀 Starting Full Daily Sentence Generation Test...\n');

  try {
    // Step 1: Generate sentence with Gemini AI
    const sentenceData = await generateSentenceWithGemini();
    
    // Step 2: Search YouTube video
    const videoData = await searchYouTubeVideo(sentenceData.sentence);
    
    // Step 3: Save to Firestore
    const savedData = await saveToFirestore(sentenceData.sentence, sentenceData.translation, videoData);
    
    console.log('\n🎉 Full test completed successfully!');
    console.log('📊 Final Result:');
    console.log('   📅 Date:', savedData.date);
    console.log('   🇪🇸 Spanish:', savedData.sentence);
    console.log('   🇰🇷 Korean:', savedData.translation);
    console.log('   📺 Video:', savedData.videoUrl ? 'Found' : 'Not found');

  } catch (error) {
    console.error('❌ Full test failed:', error);
    process.exit(1);
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  runFullTest().catch(console.error);
}

export { runFullTest };
