import * as logger from 'firebase-functions/logger';
import { openaiService } from './openai.service';
import { youtubeService } from './youtube.service';
import { firestoreService, DailySentence } from './firestore.service';

/**
 * Main service for handling daily sentence generation and management
 */
export class DailySentenceService {
  private static instance: DailySentenceService;

  private constructor() {}

  public static getInstance(): DailySentenceService {
    if (!DailySentenceService.instance) {
      DailySentenceService.instance = new DailySentenceService();
    }
    return DailySentenceService.instance;
  }

  /**
   * Generate and save today's daily sentence with video
   */
  public async generateTodaysSentence(): Promise<DailySentence> {
    const today = this.getTodayDateString();
    
    try {
      // Check if today's sentence already exists
      const existingSentence = await firestoreService.getDailySentenceByDate(today);
      if (existingSentence) {
        logger.info('Daily sentence already exists for today:', today);
        return existingSentence;
      }

      logger.info('Generating new daily sentence for:', today);

      // Step 1: Generate Spanish sentence and translation using OpenAI
      const { sentence, translation } = await openaiService.generateDailySentence();

      // Step 2: Generate YouTube search keywords
      const searchKeywords = await openaiService.generateYouTubeSearchKeywords(sentence);

      // Step 3: Find related YouTube video
      const video = await youtubeService.findBestVideoForSentence(searchKeywords, sentence);

      // Step 4: Save to Firestore
      await firestoreService.saveDailySentence(sentence, translation, today, video);

      // Step 5: Retrieve and return the saved data
      const savedSentence = await firestoreService.getDailySentenceByDate(today);
      if (!savedSentence) {
        throw new Error('Failed to retrieve saved sentence');
      }

      logger.info('Successfully generated and saved daily sentence', {
        date: today,
        sentence,
        hasVideo: !!video
      });

      return savedSentence;

    } catch (error) {
      logger.error('Error generating today\'s sentence:', error);
      throw new Error(`Failed to generate today's sentence: ${error}`);
    }
  }

  /**
   * Get daily sentence for a specific date
   */
  public async getDailySentence(date?: string): Promise<DailySentence | null> {
    const targetDate = date || this.getTodayDateString();
    
    try {
      return await firestoreService.getDailySentenceByDate(targetDate);
    } catch (error) {
      logger.error('Error getting daily sentence:', error);
      throw new Error(`Failed to get daily sentence: ${error}`);
    }
  }

  /**
   * Regenerate video for existing sentence (if video search failed initially)
   */
  public async regenerateVideoForDate(date: string): Promise<void> {
    try {
      const existingSentence = await firestoreService.getDailySentenceByDate(date);
      if (!existingSentence) {
        throw new Error(`No sentence found for date: ${date}`);
      }

      // Generate new search keywords
      const searchKeywords = await openaiService.generateYouTubeSearchKeywords(existingSentence.sentence);

      // Find new video
      const video = await youtubeService.findBestVideoForSentence(searchKeywords, existingSentence.sentence);

      if (video) {
        await firestoreService.updateVideoInfo(date, video);
        logger.info('Successfully regenerated video for date:', date);
      } else {
        logger.warn('No suitable video found for regeneration:', date);
      }

    } catch (error) {
      logger.error('Error regenerating video:', error);
      throw new Error(`Failed to regenerate video: ${error}`);
    }
  }

  /**
   * Get recent sentences for admin/debugging purposes
   */
  public async getRecentSentences(limit: number = 10): Promise<DailySentence[]> {
    try {
      return await firestoreService.getRecentSentences(limit);
    } catch (error) {
      logger.error('Error getting recent sentences:', error);
      throw new Error(`Failed to get recent sentences: ${error}`);
    }
  }

  /**
   * Manual sentence generation for specific date (admin function)
   */
  public async generateSentenceForDate(date: string, forceRegenerate: boolean = false): Promise<DailySentence> {
    try {
      // Check if sentence already exists
      if (!forceRegenerate) {
        const existingSentence = await firestoreService.getDailySentenceByDate(date);
        if (existingSentence) {
          logger.info('Daily sentence already exists for date:', date);
          return existingSentence;
        }
      }

      logger.info('Generating daily sentence for date:', date);

      // Generate sentence and translation
      const { sentence, translation } = await openaiService.generateDailySentence();

      // Generate search keywords and find video
      const searchKeywords = await openaiService.generateYouTubeSearchKeywords(sentence);
      const video = await youtubeService.findBestVideoForSentence(searchKeywords, sentence);

      // Save to Firestore
      await firestoreService.saveDailySentence(sentence, translation, date, video);

      // Retrieve and return saved data
      const savedSentence = await firestoreService.getDailySentenceByDate(date);
      if (!savedSentence) {
        throw new Error('Failed to retrieve saved sentence');
      }

      logger.info('Successfully generated sentence for date:', date);
      return savedSentence;

    } catch (error) {
      logger.error('Error generating sentence for date:', error);
      throw new Error(`Failed to generate sentence for date: ${error}`);
    }
  }

  /**
   * Get today's date in YYYY-MM-DD format (KST timezone)
   */
  private getTodayDateString(): string {
    const now = new Date();
    // Convert to KST (UTC+9)
    const kstOffset = 9 * 60; // 9 hours in minutes
    const kstTime = new Date(now.getTime() + (kstOffset * 60 * 1000));
    
    return kstTime.toISOString().split('T')[0];
  }
}

// Export singleton instance
export const dailySentenceService = DailySentenceService.getInstance();
