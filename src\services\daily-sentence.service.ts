import * as logger from "firebase-functions/logger";
import { geminiService } from "./gemini.service";
import { firestoreService, DailySentence } from "./firestore.service";

/**
 * Main service for handling daily sentence generation and management
 */
export class DailySentenceService {
  private static instance: DailySentenceService;

  private constructor() {}

  public static getInstance(): DailySentenceService {
    if (!DailySentenceService.instance) {
      DailySentenceService.instance = new DailySentenceService();
    }
    return DailySentenceService.instance;
  }

  /**
   * Generate and save today's daily sentence with video
   */
  public async generateTodaysSentence(): Promise<DailySentence> {
    const today = this.getTodayDateString();

    try {
      // Check if today's sentence already exists
      let existingSentence = null;
      try {
        existingSentence = await firestoreService.getDailySentenceByDate(today);
      } catch (error) {
        // Document doesn't exist, which is fine - we'll create a new one
        logger.info(
          "No existing sentence found for today, will create new one:",
          today
        );
      }

      if (existingSentence) {
        logger.info("Daily sentence already exists for today:", today);
        return existingSentence;
      }

      logger.info("Generating new daily sentence for:", today);

      // Step 1: Generate Spanish sentence, translation, and situation dialogue using Gemini AI
      const { sentence, translation, situation, situationTranslation } =
        await geminiService.generateDailySentence();

      // Step 2: Save to Firestore
      await firestoreService.saveDailySentence(
        sentence,
        translation,
        today,
        situation,
        situationTranslation
      );

      // Step 5: Retrieve and return the saved data
      const savedSentence = await firestoreService.getDailySentenceByDate(
        today
      );
      if (!savedSentence) {
        throw new Error("Failed to retrieve saved sentence");
      }

      logger.info("Successfully generated and saved daily sentence", {
        date: today,
        sentence,
        hasSituation: !!situation,
      });

      return savedSentence;
    } catch (error) {
      logger.error("Error generating today's sentence:", error);
      throw new Error(`Failed to generate today's sentence: ${error}`);
    }
  }

  /**
   * Get daily sentence for a specific date
   */
  public async getDailySentence(date?: string): Promise<DailySentence | null> {
    const targetDate = date || this.getTodayDateString();

    try {
      return await firestoreService.getDailySentenceByDate(targetDate);
    } catch (error) {
      logger.error("Error getting daily sentence:", error);
      throw new Error(`Failed to get daily sentence: ${error}`);
    }
  }

  /**
   * Get recent sentences for admin/debugging purposes
   */
  public async getRecentSentences(
    limit: number = 10
  ): Promise<DailySentence[]> {
    try {
      return await firestoreService.getRecentSentences(limit);
    } catch (error) {
      logger.error("Error getting recent sentences:", error);
      throw new Error(`Failed to get recent sentences: ${error}`);
    }
  }

  /**
   * Manual sentence generation for specific date (admin function)
   */
  public async generateSentenceForDate(
    date: string,
    forceRegenerate: boolean = false
  ): Promise<DailySentence> {
    try {
      // Check if sentence already exists
      if (!forceRegenerate) {
        const existingSentence = await firestoreService.getDailySentenceByDate(
          date
        );
        if (existingSentence) {
          logger.info("Daily sentence already exists for date:", date);
          return existingSentence;
        }
      }

      logger.info("Generating daily sentence for date:", date);

      // Generate sentence, translation, and situation dialogue
      const { sentence, translation, situation, situationTranslation } =
        await geminiService.generateDailySentence();

      // Save to Firestore
      await firestoreService.saveDailySentence(
        sentence,
        translation,
        date,
        situation,
        situationTranslation
      );

      // Retrieve and return saved data
      const savedSentence = await firestoreService.getDailySentenceByDate(date);
      if (!savedSentence) {
        throw new Error("Failed to retrieve saved sentence");
      }

      logger.info("Successfully generated sentence for date:", date);
      return savedSentence;
    } catch (error) {
      logger.error("Error generating sentence for date:", error);
      throw new Error(`Failed to generate sentence for date: ${error}`);
    }
  }

  /**
   * Get today's date in YYYY-MM-DD format (KST timezone)
   */
  private getTodayDateString(): string {
    const now = new Date();
    // Convert to KST (UTC+9)
    const kstOffset = 9 * 60; // 9 hours in minutes
    const kstTime = new Date(now.getTime() + kstOffset * 60 * 1000);

    return kstTime.toISOString().split("T")[0];
  }
}

// Export singleton instance
export const dailySentenceService = DailySentenceService.getInstance();
