/**
 * Daily Spanish App - Firebase Functions
 *
 * This module contains Firebase Functions for the Daily Spanish learning app.
 * Features:
 * - Daily sentence generation using OpenAI
 * - YouTube video search and integration
 * - Firestore data management
 * - Scheduled tasks for automation
 */

import { setGlobalOptions } from "firebase-functions";
import { onRequest } from "firebase-functions/https";
import { onSchedule } from "firebase-functions/v2/scheduler";
import * as logger from "firebase-functions/logger";
import cors from "cors";

// Import services
import { dailySentenceService } from "./services/daily-sentence.service";
import { environment } from "./config/environment";

// Configure CORS
const corsHandler = cors({ origin: true });

// Global options for cost control
setGlobalOptions({ maxInstances: 10 });

/**
 * Scheduled function to generate daily sentence at midnight KST
 * Runs every day at 00:00 KST (15:00 UTC previous day)
 */
export const generateDailySentence = onSchedule(
  {
    schedule: "0 15 * * *", // 15:00 UTC = 00:00 KST next day
    timeZone: "UTC",
    memory: "512MiB",
    timeoutSeconds: 300,
  },
  async () => {
    try {
      logger.info("Starting scheduled daily sentence generation");

      const result = await dailySentenceService.generateTodaysSentence();

      logger.info("Daily sentence generation completed successfully", {
        date: result.date,
        sentence: result.sentence,
        hasVideo: !!result.videoUrl,
      });
    } catch (error) {
      logger.error("Failed to generate daily sentence:", error);
      throw error;
    }
  }
);

/**
 * HTTP endpoint to get today's daily sentence
 * GET /getTodaysSentence
 */
export const getTodaysSentence = onRequest(
  { cors: true, memory: "256MiB" },
  async (request, response) => {
    corsHandler(request, response, async () => {
      try {
        logger.info("Getting today's sentence");

        const sentence = await dailySentenceService.getDailySentence();

        if (!sentence) {
          response.status(404).json({
            success: false,
            message: "No sentence found for today",
          });
          return;
        }

        response.status(200).json({
          success: true,
          data: sentence,
        });
      } catch (error) {
        logger.error("Error getting today's sentence:", error);
        response.status(500).json({
          success: false,
          message: "Internal server error",
          error: environment.isDevelopment() ? error : undefined,
        });
      }
    });
  }
);

/**
 * HTTP endpoint to get sentence for specific date
 * GET /getSentenceByDate?date=YYYY-MM-DD
 */
export const getSentenceByDate = onRequest(
  { cors: true, memory: "256MiB" },
  async (request, response) => {
    corsHandler(request, response, async () => {
      try {
        const date = request.query.date as string;

        if (!date) {
          response.status(400).json({
            success: false,
            message: "Date parameter is required (format: YYYY-MM-DD)",
          });
          return;
        }

        // Validate date format
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (!dateRegex.test(date)) {
          response.status(400).json({
            success: false,
            message: "Invalid date format. Use YYYY-MM-DD",
          });
          return;
        }

        logger.info("Getting sentence for date:", date);

        const sentence = await dailySentenceService.getDailySentence(date);

        if (!sentence) {
          response.status(404).json({
            success: false,
            message: `No sentence found for date: ${date}`,
          });
          return;
        }

        response.status(200).json({
          success: true,
          data: sentence,
        });
      } catch (error) {
        logger.error("Error getting sentence by date:", error);
        response.status(500).json({
          success: false,
          message: "Internal server error",
          error: environment.isDevelopment() ? error : undefined,
        });
      }
    });
  }
);

/**
 * HTTP endpoint to manually generate today's sentence (admin function)
 * POST /generateTodaysSentence
 */
export const manualGenerateTodaysSentence = onRequest(
  { cors: true, memory: "512MiB", timeoutSeconds: 300 },
  async (request, response) => {
    corsHandler(request, response, async () => {
      try {
        if (request.method !== "POST") {
          response.status(405).json({
            success: false,
            message: "Method not allowed. Use POST.",
          });
          return;
        }

        logger.info("Manual generation of today's sentence requested");

        const result = await dailySentenceService.generateTodaysSentence();

        response.status(200).json({
          success: true,
          message: "Daily sentence generated successfully",
          data: result,
        });
      } catch (error) {
        logger.error("Error manually generating today's sentence:", error);
        response.status(500).json({
          success: false,
          message: "Failed to generate daily sentence",
          error: environment.isDevelopment() ? error : undefined,
        });
      }
    });
  }
);

/**
 * HTTP endpoint to get recent sentences (admin function)
 * GET /getRecentSentences?limit=10
 */
export const getRecentSentences = onRequest(
  { cors: true, memory: "256MiB" },
  async (request, response) => {
    corsHandler(request, response, async () => {
      try {
        const limitParam = request.query.limit as string;
        const limit = limitParam ? parseInt(limitParam, 10) : 10;

        if (isNaN(limit) || limit < 1 || limit > 50) {
          response.status(400).json({
            success: false,
            message: "Invalid limit parameter. Must be between 1 and 50.",
          });
          return;
        }

        logger.info("Getting recent sentences with limit:", limit);

        const sentences = await dailySentenceService.getRecentSentences(limit);

        response.status(200).json({
          success: true,
          data: sentences,
          count: sentences.length,
        });
      } catch (error) {
        logger.error("Error getting recent sentences:", error);
        response.status(500).json({
          success: false,
          message: "Internal server error",
          error: environment.isDevelopment() ? error : undefined,
        });
      }
    });
  }
);

/**
 * HTTP endpoint to regenerate video for a specific date (admin function)
 * POST /regenerateVideo
 * Body: { "date": "YYYY-MM-DD" }
 */
export const regenerateVideo = onRequest(
  { cors: true, memory: "512MiB", timeoutSeconds: 300 },
  async (request, response) => {
    corsHandler(request, response, async () => {
      try {
        if (request.method !== "POST") {
          response.status(405).json({
            success: false,
            message: "Method not allowed. Use POST.",
          });
          return;
        }

        const { date } = request.body;

        if (!date) {
          response.status(400).json({
            success: false,
            message: "Date is required in request body (format: YYYY-MM-DD)",
          });
          return;
        }

        // Validate date format
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (!dateRegex.test(date)) {
          response.status(400).json({
            success: false,
            message: "Invalid date format. Use YYYY-MM-DD",
          });
          return;
        }

        logger.info("Regenerating video for date:", date);

        await dailySentenceService.regenerateVideoForDate(date);

        response.status(200).json({
          success: true,
          message: `Video regenerated successfully for date: ${date}`,
        });
      } catch (error) {
        logger.error("Error regenerating video:", error);
        response.status(500).json({
          success: false,
          message: "Failed to regenerate video",
          error: environment.isDevelopment() ? error : undefined,
        });
      }
    });
  }
);

/**
 * Health check endpoint
 * GET /health
 */
export const health = onRequest(
  { cors: true, memory: "128MiB" },
  async (request, response) => {
    corsHandler(request, response, async () => {
      try {
        // Basic health check
        const healthStatus = {
          status: "healthy",
          timestamp: new Date().toISOString(),
          environment: environment.isDevelopment()
            ? "development"
            : "production",
          services: {
            openai: "configured",
            youtube: "configured",
            firestore: "configured",
          },
        };

        response.status(200).json({
          success: true,
          data: healthStatus,
        });
      } catch (error) {
        logger.error("Health check failed:", error);
        response.status(500).json({
          success: false,
          message: "Health check failed",
          error: environment.isDevelopment() ? error : undefined,
        });
      }
    });
  }
);
