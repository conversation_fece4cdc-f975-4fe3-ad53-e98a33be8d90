/**
 * Daily Spanish App - Firebase Functions
 *
 * This module contains Firebase Functions for the Daily Spanish learning app.
 * Features:
 * - Daily sentence generation using OpenAI
 * - YouTube video search and integration
 * - Firestore data management
 * - Scheduled tasks for automation
 */

import { setGlobalOptions } from "firebase-functions";
import { onRequest } from "firebase-functions/https";
import { onSchedule } from "firebase-functions/v2/scheduler";
import { defineSecret } from "firebase-functions/params";
import * as logger from "firebase-functions/logger";
import cors from "cors";

// Import services
import { dailySentenceService } from "./services/daily-sentence.service";
import { environment } from "./config/environment";

// Define secrets
const geminiApiKey = defineSecret("GEMINI_API_KEY");
const youtubeApiKey = defineSecret("YOUTUBE_API_KEY");

// Configure CORS
const corsHandler = cors({ origin: true });

// Global options for cost control
setGlobalOptions({ maxInstances: 10 });

/**
 * 매일 자정(KST)에 자동으로 실행되는 스케줄 함수
 * - 매일 00:00 KST (15:00 UTC 전날)에 실행
 * - Gemini AI로 스페인어 문장과 한글 번역 생성
 * - YouTube에서 관련 교육 영상 검색
 * - Firestore에 데이터 저장
 */
export const generateDailySentence = onSchedule(
  {
    schedule: "0 15 * * *", // 15:00 UTC = 00:00 KST next day
    timeZone: "UTC",
    memory: "512MiB",
    timeoutSeconds: 300,
    secrets: [geminiApiKey, youtubeApiKey],
  },
  async () => {
    try {
      logger.info("Starting scheduled daily sentence generation");

      const result = await dailySentenceService.generateTodaysSentence();

      logger.info("Daily sentence generation completed successfully", {
        date: result.date,
        sentence: result.sentence,
        hasVideo: !!result.videoUrl,
      });
    } catch (error) {
      logger.error("Failed to generate daily sentence:", error);
      throw error;
    }
  }
);

/**
 * 오늘의 스페인어 문장을 조회하는 HTTP 엔드포인트
 * GET /getTodaysSentence
 * - 오늘 날짜의 스페인어 문장과 한글 번역 반환
 * - YouTube 영상 정보도 함께 반환 (있는 경우)
 * - 문장이 없으면 404 에러 반환
 */
export const getTodaysSentence = onRequest(
  {
    cors: true,
    memory: "256MiB",
    secrets: [geminiApiKey, youtubeApiKey],
    invoker: "public",
  },
  async (request, response) => {
    corsHandler(request, response, async () => {
      try {
        logger.info("Getting today's sentence");

        const sentence = await dailySentenceService.getDailySentence();

        if (!sentence) {
          response.status(404).json({
            success: false,
            message: "No sentence found for today",
          });
          return;
        }

        response.status(200).json({
          success: true,
          data: sentence,
        });
      } catch (error) {
        logger.error("Error getting today's sentence:", error);
        response.status(500).json({
          success: false,
          message: "Internal server error",
          error: environment.isDevelopment() ? error : undefined,
        });
      }
    });
  }
);

/**
 * 특정 날짜의 스페인어 문장을 조회하는 HTTP 엔드포인트
 * GET /getSentenceByDate?date=YYYY-MM-DD
 * - 지정된 날짜의 스페인어 문장과 한글 번역 반환
 * - date 파라미터는 YYYY-MM-DD 형식으로 전달
 * - 해당 날짜의 문장이 없으면 404 에러 반환
 */
export const getSentenceByDate = onRequest(
  {
    cors: true,
    memory: "256MiB",
    secrets: [geminiApiKey, youtubeApiKey],
    invoker: "public",
  },
  async (request, response) => {
    corsHandler(request, response, async () => {
      try {
        const date = request.query.date as string;

        if (!date) {
          response.status(400).json({
            success: false,
            message: "Date parameter is required (format: YYYY-MM-DD)",
          });
          return;
        }

        // Validate date format
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (!dateRegex.test(date)) {
          response.status(400).json({
            success: false,
            message: "Invalid date format. Use YYYY-MM-DD",
          });
          return;
        }

        logger.info("Getting sentence for date:", date);

        const sentence = await dailySentenceService.getDailySentence(date);

        if (!sentence) {
          response.status(404).json({
            success: false,
            message: `No sentence found for date: ${date}`,
          });
          return;
        }

        response.status(200).json({
          success: true,
          data: sentence,
        });
      } catch (error) {
        logger.error("Error getting sentence by date:", error);
        response.status(500).json({
          success: false,
          message: "Internal server error",
          error: environment.isDevelopment() ? error : undefined,
        });
      }
    });
  }
);

/**
 * 수동으로 오늘의 문장을 생성하는 HTTP 엔드포인트 (관리자용)
 * POST /manualGenerateTodaysSentence
 * - 즉시 새로운 스페인어 문장과 한글 번역 생성
 * - YouTube에서 관련 영상 검색 및 저장
 * - 이미 오늘 문장이 있어도 새로 생성하지 않음 (중복 방지)
 * - 테스트 및 디버깅 목적으로 사용
 */
export const manualGenerateTodaysSentence = onRequest(
  {
    cors: true,
    memory: "512MiB",
    timeoutSeconds: 300,
    secrets: [geminiApiKey, youtubeApiKey],
    invoker: "public",
  },
  async (request, response) => {
    corsHandler(request, response, async () => {
      try {
        if (request.method !== "POST") {
          response.status(405).json({
            success: false,
            message: "Method not allowed. Use POST.",
          });
          return;
        }

        logger.info("Manual generation of today's sentence requested");

        const result = await dailySentenceService.generateTodaysSentence();

        response.status(200).json({
          success: true,
          message: "Daily sentence generated successfully",
          data: result,
        });
      } catch (error) {
        logger.error("Error manually generating today's sentence:", error);
        response.status(500).json({
          success: false,
          message: "Failed to generate daily sentence",
          error: environment.isDevelopment() ? error : undefined,
        });
      }
    });
  }
);

/**
 * 최근 생성된 문장들을 조회하는 HTTP 엔드포인트 (관리자용)
 * GET /getRecentSentences?limit=10
 * - 최근 생성된 스페인어 문장들을 생성일 순으로 반환
 * - limit 파라미터로 조회할 개수 지정 (기본값: 10, 최대: 50)
 * - 관리 및 모니터링 목적으로 사용
 */
export const getRecentSentences = onRequest(
  {
    cors: true,
    memory: "256MiB",
    secrets: [geminiApiKey, youtubeApiKey],
    invoker: "public",
  },
  async (request, response) => {
    corsHandler(request, response, async () => {
      try {
        const limitParam = request.query.limit as string;
        const limit = limitParam ? parseInt(limitParam, 10) : 10;

        if (isNaN(limit) || limit < 1 || limit > 50) {
          response.status(400).json({
            success: false,
            message: "Invalid limit parameter. Must be between 1 and 50.",
          });
          return;
        }

        logger.info("Getting recent sentences with limit:", limit);

        const sentences = await dailySentenceService.getRecentSentences(limit);

        response.status(200).json({
          success: true,
          data: sentences,
          count: sentences.length,
        });
      } catch (error) {
        logger.error("Error getting recent sentences:", error);
        response.status(500).json({
          success: false,
          message: "Internal server error",
          error: environment.isDevelopment() ? error : undefined,
        });
      }
    });
  }
);

/**
 * 특정 날짜 문장의 YouTube 영상을 재생성하는 HTTP 엔드포인트 (관리자용)
 * POST /regenerateVideo
 * Body: { "date": "YYYY-MM-DD" }
 * - 기존 문장은 유지하고 YouTube 영상만 새로 검색
 * - 영상이 적절하지 않거나 없을 때 사용
 * - 문장이 존재하지 않으면 에러 반환
 */
export const regenerateVideo = onRequest(
  {
    cors: true,
    memory: "512MiB",
    timeoutSeconds: 300,
    secrets: [geminiApiKey, youtubeApiKey],
    invoker: "public",
  },
  async (request, response) => {
    corsHandler(request, response, async () => {
      try {
        if (request.method !== "POST") {
          response.status(405).json({
            success: false,
            message: "Method not allowed. Use POST.",
          });
          return;
        }

        const { date } = request.body;

        if (!date) {
          response.status(400).json({
            success: false,
            message: "Date is required in request body (format: YYYY-MM-DD)",
          });
          return;
        }

        // Validate date format
        const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
        if (!dateRegex.test(date)) {
          response.status(400).json({
            success: false,
            message: "Invalid date format. Use YYYY-MM-DD",
          });
          return;
        }

        logger.info("Regenerating video for date:", date);

        await dailySentenceService.regenerateVideoForDate(date);

        response.status(200).json({
          success: true,
          message: `Video regenerated successfully for date: ${date}`,
        });
      } catch (error) {
        logger.error("Error regenerating video:", error);
        response.status(500).json({
          success: false,
          message: "Failed to regenerate video",
          error: environment.isDevelopment() ? error : undefined,
        });
      }
    });
  }
);

/**
 * 서비스 상태를 확인하는 헬스체크 엔드포인트
 * GET /health
 * - 서비스가 정상적으로 작동하는지 확인
 * - API 키 설정 상태 및 환경 정보 반환
 * - 모니터링 및 로드밸런서에서 사용
 */
export const health = onRequest(
  { cors: true, memory: "128MiB", invoker: "public" },
  async (request, response) => {
    corsHandler(request, response, async () => {
      try {
        // Basic health check
        const healthStatus = {
          status: "healthy",
          timestamp: new Date().toISOString(),
          environment: environment.isDevelopment()
            ? "development"
            : "production",
          services: {
            openai: "configured",
            youtube: "configured",
            firestore: "configured",
          },
        };

        response.status(200).json({
          success: true,
          data: healthStatus,
        });
      } catch (error) {
        logger.error("Health check failed:", error);
        response.status(500).json({
          success: false,
          message: "Health check failed",
          error: environment.isDevelopment() ? error : undefined,
        });
      }
    });
  }
);
