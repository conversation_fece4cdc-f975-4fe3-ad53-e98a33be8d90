import { GoogleGenerativeAI } from "@google/generative-ai";
import { environment } from "../config/environment";
import * as logger from "firebase-functions/logger";

/**
 * Service for interacting with Google Gemini AI API
 */
export class GeminiService {
  private static instance: GeminiService;
  private genAI?: GoogleGenerativeAI;
  private model?: any;

  private constructor() {
    // Lazy initialization - will be set when first used
  }

  public static getInstance(): GeminiService {
    if (!GeminiService.instance) {
      GeminiService.instance = new GeminiService();
    }
    return GeminiService.instance;
  }

  private initializeIfNeeded() {
    if (!this.genAI) {
      this.genAI = new GoogleGenerativeAI(environment.getGeminiApiKey());
      this.model = this.genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
    }
  }

  /**
   * Generate a Spanish sentence with Korean translation
   */
  public async generateDailySentence(): Promise<{
    sentence: string;
    translation: string;
  }> {
    try {
      this.initializeIfNeeded();

      const prompt = `Generate a simple Spanish sentence for daily conversation with Korean translation. Respond only in JSON format:
{"sentence": "Spanish sentence", "translation": "Korean translation"}

Example: {"sentence": "¿Dónde está el baño?", "translation": "화장실이 어디에요?"}`;

      const result = await this.model!.generateContent(prompt);
      const response = await result.response;
      const text = response.text();

      if (!text) {
        throw new Error("No response from Gemini AI");
      }

      // Clean the response text and parse JSON
      const cleanedText = text.trim().replace(/```json\n?|\n?```/g, "");
      const parsedResponse = JSON.parse(cleanedText);

      if (!parsedResponse.sentence || !parsedResponse.translation) {
        throw new Error("Invalid response format from Gemini AI");
      }

      logger.info("Successfully generated daily sentence", {
        sentence: parsedResponse.sentence,
        translation: parsedResponse.translation,
      });

      return {
        sentence: parsedResponse.sentence,
        translation: parsedResponse.translation,
      };
    } catch (error) {
      logger.error("Error generating daily sentence:", error);
      throw new Error(`Failed to generate daily sentence: ${error}`);
    }
  }

  /**
   * Generate search keywords for YouTube based on the Spanish sentence
   */
  public async generateYouTubeSearchKeywords(
    sentence: string
  ): Promise<string> {
    try {
      this.initializeIfNeeded();

      const prompt = `Generate YouTube search keywords for Spanish learning videos related to: "${sentence}"
Return only keywords as a single string.
Example: "español conversación baño preguntar direcciones"`;

      const result = await this.model!.generateContent(prompt);
      const response = await result.response;
      const keywords = response.text().trim();

      if (!keywords) {
        throw new Error("No keywords generated from Gemini AI");
      }

      logger.info("Generated YouTube search keywords", { keywords });
      return keywords;
    } catch (error) {
      logger.error("Error generating YouTube keywords:", error);
      // Fallback to basic keywords if AI fails
      return `español conversación aprender`;
    }
  }
}

// Export singleton instance - lazy initialization
export const geminiService = GeminiService.getInstance();
