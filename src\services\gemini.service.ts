import { GoogleGenerativeAI } from "@google/generative-ai";
import { environment } from "../config/environment";
import * as logger from "firebase-functions/logger";

/**
 * Service for interacting with Google Gemini AI API
 */
export class GeminiService {
  private static instance: GeminiService;
  private genAI: GoogleGenerativeAI;
  private model: any;

  private constructor() {
    this.genAI = new GoogleGenerativeAI(environment.getGeminiApiKey());
    this.model = this.genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
  }

  public static getInstance(): GeminiService {
    if (!GeminiService.instance) {
      GeminiService.instance = new GeminiService();
    }
    return GeminiService.instance;
  }

  /**
   * Generate a Spanish sentence with Korean translation
   */
  public async generateDailySentence(): Promise<{
    sentence: string;
    translation: string;
  }> {
    try {
      const prompt = `
        Generate a useful Spanish sentence for daily conversation learning.
        The sentence should be:
        - Practical and commonly used in everyday situations
        - Appropriate for beginner to intermediate learners
        - Not too complex but meaningful
        
        Please respond in the following JSON format only, without any additional text:
        {
          "sentence": "Spanish sentence here",
          "translation": "Korean translation here"
        }
        
        Example:
        {
          "sentence": "¿Dónde está el baño?",
          "translation": "화장실이 어디에요?"
        }
      `;

      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      const text = response.text();

      if (!text) {
        throw new Error("No response from Gemini AI");
      }

      // Clean the response text and parse JSON
      const cleanedText = text.trim().replace(/```json\n?|\n?```/g, "");
      const parsedResponse = JSON.parse(cleanedText);

      if (!parsedResponse.sentence || !parsedResponse.translation) {
        throw new Error("Invalid response format from Gemini AI");
      }

      logger.info("Successfully generated daily sentence", {
        sentence: parsedResponse.sentence,
        translation: parsedResponse.translation,
      });

      return {
        sentence: parsedResponse.sentence,
        translation: parsedResponse.translation,
      };
    } catch (error) {
      logger.error("Error generating daily sentence:", error);
      throw new Error(`Failed to generate daily sentence: ${error}`);
    }
  }

  /**
   * Generate search keywords for YouTube based on the Spanish sentence
   */
  public async generateYouTubeSearchKeywords(
    sentence: string
  ): Promise<string> {
    try {
      const prompt = `
        Given this Spanish sentence: "${sentence}"
        
        Generate appropriate YouTube search keywords in Spanish that would help find educational videos related to this phrase.
        The keywords should be:
        - Relevant to the sentence context
        - Likely to return Spanish learning or conversation videos
        - Simple and effective for YouTube search
        
        Return only the search keywords as a single string, no additional formatting or explanation.
        
        Example: For "¿Dónde está el baño?" return "español conversación baño preguntar direcciones"
      `;

      const result = await this.model.generateContent(prompt);
      const response = await result.response;
      const keywords = response.text().trim();

      if (!keywords) {
        throw new Error("No keywords generated from Gemini AI");
      }

      logger.info("Generated YouTube search keywords", { keywords });
      return keywords;
    } catch (error) {
      logger.error("Error generating YouTube keywords:", error);
      // Fallback to basic keywords if AI fails
      return `español conversación aprender`;
    }
  }
}

// Export singleton instance
export const geminiService = GeminiService.getInstance();
