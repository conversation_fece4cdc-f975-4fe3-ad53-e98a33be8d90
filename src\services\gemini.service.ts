import { GoogleGenerativeAI } from "@google/generative-ai";
import { environment } from "../config/environment";
import * as logger from "firebase-functions/logger";

/**
 * Service for interacting with Google Gemini AI API
 */
export class GeminiService {
  private static instance: GeminiService;
  private genAI?: GoogleGenerativeAI;
  private model?: any;

  private constructor() {
    // Lazy initialization - will be set when first used
  }

  public static getInstance(): GeminiService {
    if (!GeminiService.instance) {
      GeminiService.instance = new GeminiService();
    }
    return GeminiService.instance;
  }

  private initializeIfNeeded() {
    if (!this.genAI) {
      this.genAI = new GoogleGenerativeAI(environment.getGeminiApiKey());
      this.model = this.genAI.getGenerativeModel({ model: "gemini-1.5-flash" });
    }
  }

  /**
   * Generate a Spanish sentence with Korean translation
   */
  public async generateDailySentence(): Promise<{
    sentence: string;
    translation: string;
    situation: string;
    situationTranslation: string;
  }> {
    try {
      this.initializeIfNeeded();

      const prompt = `Generate Spanish daily conversation with situation dialogue. JSON only:
{
  "sentence": "main Spanish sentence",
  "translation": "Korean translation",
  "situation": "A: Spanish dialogue line 1\\nB: Spanish dialogue line 2",
  "situationTranslation": "A: Korean translation 1\\nB: Korean translation 2"
}

Example:
{"sentence": "¿Dónde está el baño?", "translation": "화장실이 어디에요?", "situation": "A: Disculpe, ¿dónde está el baño?\\nB: El baño está al fondo a la derecha.", "situationTranslation": "A: 실례합니다, 화장실이 어디에요?\\nB: 화장실은 안쪽 오른편에 있습니다."}`;

      const result = await this.model!.generateContent(prompt);
      const response = await result.response;
      const text = response.text();

      if (!text) {
        throw new Error("No response from Gemini AI");
      }

      // Clean the response text and parse JSON
      const cleanedText = text.trim().replace(/```json\n?|\n?```/g, "");
      const parsedResponse = JSON.parse(cleanedText);

      if (
        !parsedResponse.sentence ||
        !parsedResponse.translation ||
        !parsedResponse.situation ||
        !parsedResponse.situationTranslation
      ) {
        throw new Error(
          "Invalid response format from Gemini AI - missing required fields"
        );
      }

      logger.info("Successfully generated daily sentence", {
        sentence: parsedResponse.sentence,
        translation: parsedResponse.translation,
        situation: parsedResponse.situation,
        situationTranslation: parsedResponse.situationTranslation,
      });

      return {
        sentence: parsedResponse.sentence,
        translation: parsedResponse.translation,
        situation: parsedResponse.situation,
        situationTranslation: parsedResponse.situationTranslation,
      };
    } catch (error) {
      logger.error("Error generating daily sentence:", error);
      throw new Error(`Failed to generate daily sentence: ${error}`);
    }
  }
}

// Export singleton instance - lazy initialization
export const geminiService = GeminiService.getInstance();
