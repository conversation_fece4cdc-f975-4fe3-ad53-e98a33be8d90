# 하루 한 문장 스페인어 앱 - Firebase Functions

Firebase Functions를 사용한 하루 한 문장 스페인어 학습 앱의 백엔드 서비스입니다.

## 🚀 주요 기능

- **자동 문장 생성**: OpenAI API를 사용하여 매일 새로운 스페인어 문장과 한글 번역 생성
- **YouTube 영상 검색**: 생성된 문장과 관련된 교육용 YouTube 영상 자동 검색
- **Firestore 데이터 저장**: 생성된 데이터를 Firebase Firestore에 체계적으로 저장
- **스케줄링**: 매일 자정(KST)에 자동으로 새 문장 생성
- **REST API**: Flutter 앱에서 사용할 수 있는 RESTful API 제공

## 📋 요구사항

- Node.js 22+
- Firebase CLI
- OpenAI API Key
- YouTube Data API Key

## 🛠️ 설치 및 설정

### 1. 환경변수 설정

`.env` 파일에 API 키들을 설정하세요:

```env
OPENAI_API_KEY=your_openai_api_key_here
YOUTUBE_API_KEY=your_youtube_api_key_here
```

### 2. 의존성 설치

```bash
npm install
```

### 3. 프로젝트 빌드

```bash
npm run build
```

### 4. 로컬 테스트

```bash
npm run serve
```

## 📡 API 엔드포인트

### 1. 오늘의 문장 조회
```
GET /getTodaysSentence
```

**응답 예시:**
```json
{
  "success": true,
  "data": {
    "sentence": "¿Dónde está el baño?",
    "translation": "화장실이 어디에요?",
    "date": "2025-07-17",
    "videoUrl": "https://youtube.com/watch?v=...",
    "videoThumbnail": "https://img.youtube.com/...",
    "videoTitle": "Spanish Conversation: Asking for Directions",
    "createdAt": "2025-07-17T00:00:00Z"
  }
}
```

### 2. 특정 날짜 문장 조회
```
GET /getSentenceByDate?date=2025-07-17
```

### 3. 최근 문장 목록 조회 (관리자용)
```
GET /getRecentSentences?limit=10
```

### 4. 수동 문장 생성 (관리자용)
```
POST /manualGenerateTodaysSentence
```

### 5. 영상 재생성 (관리자용)
```
POST /regenerateVideo
Content-Type: application/json

{
  "date": "2025-07-17"
}
```

### 6. 헬스 체크
```
GET /health
```

## 🕐 스케줄링

- **자동 실행**: 매일 00:00 KST (15:00 UTC 전날)에 `generateDailySentence` 함수가 자동 실행
- **처리 과정**:
  1. OpenAI로 스페인어 문장과 한글 번역 생성
  2. 문장 기반 YouTube 검색 키워드 생성
  3. YouTube Data API로 관련 영상 검색
  4. 최적의 영상 선택
  5. Firestore에 모든 데이터 저장

## 🗄️ 데이터베이스 스키마

### Firestore Collection: `daily_sentences`

```typescript
interface DailySentence {
  sentence: string;           // 스페인어 문장
  translation: string;        // 한글 번역
  date: string;              // YYYY-MM-DD 형식
  videoUrl?: string;         // YouTube 영상 URL
  videoThumbnail?: string;   // 썸네일 이미지 URL
  videoTitle?: string;       // 영상 제목
  videoDescription?: string; // 영상 설명
  channelTitle?: string;     // 채널명
  createdAt: Timestamp;      // 생성 시간
  aiSource?: string;         // AI 모델 정보
  youtubeId?: string;        // YouTube 영상 ID
  tags?: string[];           // 태그 (확장용)
}
```

## 🏗️ 프로젝트 구조

```
src/
├── config/
│   └── environment.ts          # 환경변수 관리
├── services/
│   ├── openai.service.ts       # OpenAI API 서비스
│   ├── youtube.service.ts      # YouTube Data API 서비스
│   ├── firestore.service.ts    # Firestore 데이터베이스 서비스
│   └── daily-sentence.service.ts # 메인 비즈니스 로직
├── test/
│   └── test-environment.ts     # 환경 테스트 스크립트
└── index.ts                    # Firebase Functions 엔트리포인트
```

## 🧪 테스트

환경 설정과 API 연결을 테스트하려면:

```bash
npm run build
node lib/test/test-environment.js
```

## 🚀 배포

```bash
npm run deploy
```

## 🔧 개발 명령어

- `npm run build`: TypeScript 컴파일
- `npm run build:watch`: 파일 변경 감지하여 자동 컴파일
- `npm run serve`: 로컬 에뮬레이터 실행
- `npm run shell`: Firebase Functions 셸 실행
- `npm run deploy`: Firebase에 배포
- `npm run logs`: 함수 로그 확인

## 🔒 보안 고려사항

- API 키는 환경변수로 관리하며, 배포 후 `.env` 파일 삭제
- Firebase Functions Config를 통한 안전한 환경변수 관리
- CORS 설정으로 허용된 도메인에서만 API 접근 가능

## 📝 라이센스

이 프로젝트는 개인 학습용으로 제작되었습니다.
