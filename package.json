{"name": "functions", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "22"}, "main": "lib/index.js", "dependencies": {"@types/cors": "^2.8.19", "axios": "^1.10.0", "cors": "^2.8.5", "dotenv": "^17.2.0", "firebase-admin": "^12.6.0", "firebase-functions": "^6.0.1", "openai": "^5.9.2"}, "devDependencies": {"firebase-functions-test": "^3.1.0", "typescript": "^5.7.3"}, "private": true}