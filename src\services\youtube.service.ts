import axios from "axios";
import { environment } from "../config/environment";
import * as logger from "firebase-functions/logger";

/**
 * YouTube video information interface
 */
export interface YouTubeVideo {
  videoId: string;
  videoUrl: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  channelTitle: string;
  publishedAt: string;
  duration?: string; // ISO 8601 duration format (PT1M30S = 1 minute 30 seconds)
  durationSeconds?: number; // Duration in seconds for easier filtering
}

/**
 * Parse ISO 8601 duration to seconds
 * Example: PT1M30S = 90 seconds, PT45S = 45 seconds
 */
function parseDurationToSeconds(duration: string): number {
  const match = duration.match(/PT(?:(\d+)H)?(?:(\d+)M)?(?:(\d+)S)?/);
  if (!match) return 0;

  const hours = parseInt(match[1] || "0", 10);
  const minutes = parseInt(match[2] || "0", 10);
  const seconds = parseInt(match[3] || "0", 10);

  return hours * 3600 + minutes * 60 + seconds;
}

/**
 * Service for interacting with YouTube Data API
 */
export class YouTubeService {
  private static instance: YouTubeService;
  private apiKey?: string;
  private baseUrl = "https://www.googleapis.com/youtube/v3";

  private constructor() {
    // Lazy initialization - will be set when first used
  }

  public static getInstance(): YouTubeService {
    if (!YouTubeService.instance) {
      YouTubeService.instance = new YouTubeService();
    }
    return YouTubeService.instance;
  }

  private initializeIfNeeded() {
    if (!this.apiKey) {
      this.apiKey = environment.getYouTubeApiKey();
    }
  }

  /**
   * Search for Spanish learning videos related to the given keywords
   */
  public async searchVideos(keywords: string): Promise<YouTubeVideo[]> {
    try {
      this.initializeIfNeeded();

      const searchQuery = `${keywords} español aprender spanish learning`;

      const response = await axios.get(`${this.baseUrl}/search`, {
        params: {
          key: this.apiKey!,
          part: "snippet",
          q: searchQuery,
          type: "video",
          maxResults: 10,
          order: "relevance",
          regionCode: "KR", // Target Korean region
          relevanceLanguage: "es", // Spanish content
          videoDefinition: "any",
          videoDuration: "medium", // Prefer medium length videos
          safeSearch: "strict",
        },
      });

      if (!response.data.items || response.data.items.length === 0) {
        logger.warn("No YouTube videos found for keywords:", keywords);
        return [];
      }

      // Get video IDs for duration lookup
      const videoIds = response.data.items.map((item: any) => item.id.videoId);

      // Get video details including duration
      const detailsResponse = await axios.get(`${this.baseUrl}/videos`, {
        params: {
          key: this.apiKey!,
          part: "contentDetails",
          id: videoIds.join(","),
        },
      });

      // Create a map of video durations
      const durationMap: { [videoId: string]: string } = {};
      if (detailsResponse.data.items) {
        detailsResponse.data.items.forEach((item: any) => {
          durationMap[item.id] = item.contentDetails.duration;
        });
      }

      const videos: YouTubeVideo[] = response.data.items.map((item: any) => {
        const duration = durationMap[item.id.videoId] || "";
        const durationSeconds = parseDurationToSeconds(duration);

        return {
          videoId: item.id.videoId,
          videoUrl: `https://www.youtube.com/watch?v=${item.id.videoId}`,
          title: item.snippet.title,
          description: item.snippet.description,
          thumbnailUrl:
            item.snippet.thumbnails.medium?.url ||
            item.snippet.thumbnails.default?.url,
          channelTitle: item.snippet.channelTitle,
          publishedAt: item.snippet.publishedAt,
          duration,
          durationSeconds,
        };
      });

      // Filter videos by duration (30-45 seconds)
      const filteredVideos = videos.filter((video) => {
        if (!video.durationSeconds) return false;
        return video.durationSeconds >= 30 && video.durationSeconds <= 45;
      });

      logger.info(
        `Found ${videos.length} YouTube videos, ${filteredVideos.length} videos match duration filter (30-45s) for keywords: ${keywords}`
      );

      // Return filtered videos, or all videos if no videos match the duration filter
      return filteredVideos.length > 0 ? filteredVideos : videos;
    } catch (error) {
      logger.error("Error searching YouTube videos:", error);
      throw new Error(`Failed to search YouTube videos: ${error}`);
    }
  }

  /**
   * Find the best video from search results based on relevance criteria
   */
  public selectBestVideo(
    videos: YouTubeVideo[],
    sentence: string
  ): YouTubeVideo | null {
    if (videos.length === 0) {
      return null;
    }

    // Simple scoring algorithm to find the most relevant video
    const scoredVideos = videos.map((video) => {
      let score = 0;
      const titleLower = video.title.toLowerCase();
      const descriptionLower = video.description.toLowerCase();

      // Prefer videos with optimal duration (30-45 seconds) - highest priority
      if (
        video.durationSeconds &&
        video.durationSeconds >= 30 &&
        video.durationSeconds <= 45
      ) {
        score += 5; // High priority for optimal duration
      } else if (
        video.durationSeconds &&
        video.durationSeconds >= 20 &&
        video.durationSeconds <= 60
      ) {
        score += 2; // Medium priority for acceptable duration
      }

      // Prefer videos with Spanish learning keywords
      const learningKeywords = [
        "español",
        "spanish",
        "learn",
        "aprender",
        "conversación",
        "conversation",
      ];
      learningKeywords.forEach((keyword) => {
        if (titleLower.includes(keyword)) score += 3;
        if (descriptionLower.includes(keyword)) score += 1;
      });

      // Prefer educational channels
      const educationalChannels = [
        "spanishpod",
        "spanish",
        "learn",
        "education",
        "tutorial",
      ];
      educationalChannels.forEach((keyword) => {
        if (video.channelTitle.toLowerCase().includes(keyword)) score += 2;
      });

      // Prefer videos with good titles (not too short, not too long)
      if (video.title.length > 20 && video.title.length < 100) {
        score += 1;
      }

      return { ...video, score };
    });

    // Sort by score and return the best one
    scoredVideos.sort((a, b) => b.score - a.score);

    const bestVideo = scoredVideos[0];
    logger.info("Selected best video:", {
      title: bestVideo.title,
      score: bestVideo.score,
      duration: bestVideo.duration,
      durationSeconds: bestVideo.durationSeconds,
      videoUrl: bestVideo.videoUrl,
    });

    return bestVideo;
  }

  /**
   * Search and select the best video for a Spanish sentence
   */
  public async findBestVideoForSentence(
    keywords: string,
    sentence: string
  ): Promise<YouTubeVideo | null> {
    try {
      const videos = await this.searchVideos(keywords);
      return this.selectBestVideo(videos, sentence);
    } catch (error) {
      logger.error("Error finding best video for sentence:", error);
      return null;
    }
  }
}

// Export singleton instance - lazy initialization
export const youtubeService = YouTubeService.getInstance();
