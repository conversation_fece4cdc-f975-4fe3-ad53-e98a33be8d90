import axios from 'axios';
import { environment } from '../config/environment';
import * as logger from 'firebase-functions/logger';

/**
 * YouTube video information interface
 */
export interface YouTubeVideo {
  videoId: string;
  videoUrl: string;
  title: string;
  description: string;
  thumbnailUrl: string;
  channelTitle: string;
  publishedAt: string;
}

/**
 * Service for interacting with YouTube Data API
 */
export class YouTubeService {
  private static instance: YouTubeService;
  private apiKey: string;
  private baseUrl = 'https://www.googleapis.com/youtube/v3';

  private constructor() {
    this.apiKey = environment.getYouTubeApiKey();
  }

  public static getInstance(): YouTubeService {
    if (!YouTubeService.instance) {
      YouTubeService.instance = new YouTubeService();
    }
    return YouTubeService.instance;
  }

  /**
   * Search for Spanish learning videos related to the given keywords
   */
  public async searchVideos(keywords: string): Promise<YouTubeVideo[]> {
    try {
      const searchQuery = `${keywords} español aprender spanish learning`;
      
      const response = await axios.get(`${this.baseUrl}/search`, {
        params: {
          key: this.apiKey,
          part: 'snippet',
          q: searchQuery,
          type: 'video',
          maxResults: 10,
          order: 'relevance',
          regionCode: 'KR', // Target Korean region
          relevanceLanguage: 'es', // Spanish content
          videoDefinition: 'any',
          videoDuration: 'medium', // Prefer medium length videos
          safeSearch: 'strict'
        }
      });

      if (!response.data.items || response.data.items.length === 0) {
        logger.warn('No YouTube videos found for keywords:', keywords);
        return [];
      }

      const videos: YouTubeVideo[] = response.data.items.map((item: any) => ({
        videoId: item.id.videoId,
        videoUrl: `https://www.youtube.com/watch?v=${item.id.videoId}`,
        title: item.snippet.title,
        description: item.snippet.description,
        thumbnailUrl: item.snippet.thumbnails.medium?.url || item.snippet.thumbnails.default?.url,
        channelTitle: item.snippet.channelTitle,
        publishedAt: item.snippet.publishedAt
      }));

      logger.info(`Found ${videos.length} YouTube videos for keywords: ${keywords}`);
      return videos;

    } catch (error) {
      logger.error('Error searching YouTube videos:', error);
      throw new Error(`Failed to search YouTube videos: ${error}`);
    }
  }

  /**
   * Find the best video from search results based on relevance criteria
   */
  public selectBestVideo(videos: YouTubeVideo[], sentence: string): YouTubeVideo | null {
    if (videos.length === 0) {
      return null;
    }

    // Simple scoring algorithm to find the most relevant video
    const scoredVideos = videos.map(video => {
      let score = 0;
      const titleLower = video.title.toLowerCase();
      const descriptionLower = video.description.toLowerCase();

      // Prefer videos with Spanish learning keywords
      const learningKeywords = ['español', 'spanish', 'learn', 'aprender', 'conversación', 'conversation'];
      learningKeywords.forEach(keyword => {
        if (titleLower.includes(keyword)) score += 3;
        if (descriptionLower.includes(keyword)) score += 1;
      });

      // Prefer educational channels
      const educationalChannels = ['spanishpod', 'spanish', 'learn', 'education', 'tutorial'];
      educationalChannels.forEach(keyword => {
        if (video.channelTitle.toLowerCase().includes(keyword)) score += 2;
      });

      // Prefer videos with good titles (not too short, not too long)
      if (video.title.length > 20 && video.title.length < 100) {
        score += 1;
      }

      return { ...video, score };
    });

    // Sort by score and return the best one
    scoredVideos.sort((a, b) => b.score - a.score);
    
    const bestVideo = scoredVideos[0];
    logger.info('Selected best video:', {
      title: bestVideo.title,
      score: bestVideo.score,
      videoUrl: bestVideo.videoUrl
    });

    return bestVideo;
  }

  /**
   * Search and select the best video for a Spanish sentence
   */
  public async findBestVideoForSentence(keywords: string, sentence: string): Promise<YouTubeVideo | null> {
    try {
      const videos = await this.searchVideos(keywords);
      return this.selectBestVideo(videos, sentence);
    } catch (error) {
      logger.error('Error finding best video for sentence:', error);
      return null;
    }
  }
}

// Export singleton instance
export const youtubeService = YouTubeService.getInstance();
