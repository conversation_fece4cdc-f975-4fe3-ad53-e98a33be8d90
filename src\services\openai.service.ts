import OpenAI from "openai";
import { environment } from "../config/environment";
import * as logger from "firebase-functions/logger";

/**
 * Service for interacting with OpenAI API
 */
export class OpenAIService {
  private static instance: OpenAIService;
  private openai: OpenAI;

  private constructor() {
    this.openai = new OpenAI({
      apiKey: environment.getOpenAIApiKey(),
    });
  }

  public static getInstance(): OpenAIService {
    if (!OpenAIService.instance) {
      OpenAIService.instance = new OpenAIService();
    }
    return OpenAIService.instance;
  }

  /**
   * Generate a Spanish sentence with Korean translation
   */
  public async generateDailySentence(): Promise<{
    sentence: string;
    translation: string;
  }> {
    try {
      const prompt = `
        Generate a useful Spanish sentence for daily conversation learning.
        The sentence should be:
        - Practical and commonly used in everyday situations
        - Appropriate for beginner to intermediate learners
        - Not too complex but meaningful
        
        Please respond in the following JSON format:
        {
          "sentence": "Spanish sentence here",
          "translation": "Korean translation here"
        }
        
        Example:
        {
          "sentence": "¿Dónde está el baño?",
          "translation": "화장실이 어디에요?"
        }
      `;

      const completion = await this.openai.chat.completions.create({
        model: "gpt-3.5-turbo",
        messages: [
          {
            role: "system",
            content:
              "You are a Spanish language teacher helping Korean students learn practical Spanish phrases.",
          },
          {
            role: "user",
            content: prompt,
          },
        ],
        temperature: 0.7,
        max_tokens: 200,
      });

      const responseContent = completion.choices[0]?.message?.content;
      if (!responseContent) {
        throw new Error("No response from OpenAI");
      }

      // Parse JSON response
      const parsedResponse = JSON.parse(responseContent);

      if (!parsedResponse.sentence || !parsedResponse.translation) {
        throw new Error("Invalid response format from OpenAI");
      }

      logger.info("Successfully generated daily sentence", {
        sentence: parsedResponse.sentence,
        translation: parsedResponse.translation,
      });

      return {
        sentence: parsedResponse.sentence,
        translation: parsedResponse.translation,
      };
    } catch (error) {
      logger.error("Error generating daily sentence:", error);
      throw new Error(`Failed to generate daily sentence: ${error}`);
    }
  }

  /**
   * Generate search keywords for YouTube based on the Spanish sentence
   */
  public async generateYouTubeSearchKeywords(
    sentence: string
  ): Promise<string> {
    try {
      const prompt = `
        Given this Spanish sentence: "${sentence}"
        
        Generate appropriate YouTube search keywords in Spanish that would help find educational videos related to this phrase.
        The keywords should be:
        - Relevant to the sentence context
        - Likely to return Spanish learning or conversation videos
        - Simple and effective for YouTube search
        
        Return only the search keywords as a single string, no additional formatting.
        
        Example: For "¿Dónde está el baño?" return "español conversación baño preguntar direcciones"
      `;

      const completion = await this.openai.chat.completions.create({
        model: "gpt-3.5-turbo",
        messages: [
          {
            role: "user",
            content: prompt,
          },
        ],
        temperature: 0.5,
        max_tokens: 50,
      });

      const keywords = completion.choices[0]?.message?.content?.trim();
      if (!keywords) {
        throw new Error("No keywords generated from OpenAI");
      }

      logger.info("Generated YouTube search keywords", { keywords });
      return keywords;
    } catch (error) {
      logger.error("Error generating YouTube keywords:", error);
      // Fallback to basic keywords if AI fails
      return `español conversación aprender`;
    }
  }
}

// Export singleton instance
export const openaiService = OpenAIService.getInstance();
