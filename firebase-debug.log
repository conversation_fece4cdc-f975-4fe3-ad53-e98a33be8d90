[debug] [2025-07-16T15:14:26.413Z] ----------------------------------------------------------------------
[debug] [2025-07-16T15:14:26.417Z] Command:       C:\nvm4w\nodejs\node.exe C:\nvm4w\nodejs\node_modules\firebase-tools\lib\bin\firebase.js emulators:start --only functions
[debug] [2025-07-16T15:14:26.418Z] CLI Version:   14.10.1
[debug] [2025-07-16T15:14:26.418Z] Platform:      win32
[debug] [2025-07-16T15:14:26.418Z] Node Version:  v22.16.0
[debug] [2025-07-16T15:14:26.418Z] Time:          Thu Jul 17 2025 00:14:26 GMT+0900 (대한민국 표준시)
[debug] [2025-07-16T15:14:26.419Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-07-16T15:14:26.691Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-07-16T15:14:26.691Z] > authorizing via signed-in user (<EMAIL>)
[info] i  emulators: Starting emulators: functions {"metadata":{"emulator":{"name":"hub"},"message":"Starting emulators: functions"}}
[debug] [2025-07-16T15:14:26.700Z] [logging] Logging Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-16T15:14:26.700Z] assigned listening specs for emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}]},"metadata":{"message":"assigned listening specs for emulators"}}
[debug] [2025-07-16T15:14:26.704Z] [hub] writing locator at C:\Users\<USER>\AppData\Local\Temp\hub-daily-spanish-57c77.json
[debug] [2025-07-16T15:14:26.720Z] [functions] Functions Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-16T15:14:26.720Z] [eventarc] Eventarc Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-16T15:14:26.720Z] [tasks] Cloud Tasks Emulator only supports listening on one address (127.0.0.1). Not listening on ::1
[debug] [2025-07-16T15:14:26.721Z] late-assigned ports for functions and eventarc emulators {"user":{"hub":[{"address":"127.0.0.1","family":"IPv4","port":4400},{"address":"::1","family":"IPv6","port":4400}],"ui":[{"address":"127.0.0.1","family":"IPv4","port":4000},{"address":"::1","family":"IPv6","port":4000}],"logging":[{"address":"127.0.0.1","family":"IPv4","port":4500}],"functions":[{"address":"127.0.0.1","family":"IPv4","port":5001}],"eventarc":[{"address":"127.0.0.1","family":"IPv4","port":9299}],"tasks":[{"address":"127.0.0.1","family":"IPv4","port":9499}]},"metadata":{"message":"late-assigned ports for functions and eventarc emulators"}}
[warn] !  functions: The following emulators are not running, calls to these services from the Functions emulator will affect production: apphosting, auth, firestore, database, hosting, pubsub, storage, dataconnect {"metadata":{"emulator":{"name":"functions"},"message":"The following emulators are not running, calls to these services from the Functions emulator will affect production: \u001b[1mapphosting, auth, firestore, database, hosting, pubsub, storage, dataconnect\u001b[22m"}}
[debug] [2025-07-16T15:14:26.736Z] defaultcredentials: writing to file C:\Users\<USER>\AppData\Roaming\firebase\waz6342_gmail_com_application_default_credentials.json
[debug] [2025-07-16T15:14:26.737Z] Setting GAC to C:\Users\<USER>\AppData\Roaming\firebase\waz6342_gmail_com_application_default_credentials.json {"metadata":{"emulator":{"name":"functions"},"message":"Setting GAC to C:\\Users\\<USER>\\AppData\\Roaming\\firebase\\waz6342_gmail_com_application_default_credentials.json"}}
[debug] [2025-07-16T15:14:26.738Z] Checked if tokens are valid: true, expires at: 1752682249142
[debug] [2025-07-16T15:14:26.738Z] Checked if tokens are valid: true, expires at: 1752682249142
[debug] [2025-07-16T15:14:26.739Z] >>> [apiv2][query] GET https://firebase.googleapis.com/v1beta1/projects/daily-spanish-57c77/adminSdkConfig [none]
[debug] [2025-07-16T15:14:27.155Z] <<< [apiv2][status] GET https://firebase.googleapis.com/v1beta1/projects/daily-spanish-57c77/adminSdkConfig 200
[debug] [2025-07-16T15:14:27.156Z] <<< [apiv2][body] GET https://firebase.googleapis.com/v1beta1/projects/daily-spanish-57c77/adminSdkConfig {"projectId":"daily-spanish-57c77","storageBucket":"daily-spanish-57c77.firebasestorage.app"}
[info] i  ui: downloading ui-v1.15.0.zip... {"metadata":{"emulator":{"name":"ui"},"message":"downloading ui-v1.15.0.zip..."}}
[debug] [2025-07-16T15:14:27.196Z] >>> [apiv2][query] GET https://storage.googleapis.com/firebase-preview-drop/emulator/ui-v1.15.0.zip 
[debug] [2025-07-16T15:14:27.478Z] <<< [apiv2][status] GET https://storage.googleapis.com/firebase-preview-drop/emulator/ui-v1.15.0.zip 200
[debug] [2025-07-16T15:14:27.478Z] <<< [apiv2][body] GET https://storage.googleapis.com/firebase-preview-drop/emulator/ui-v1.15.0.zip [stream]
[debug] [2025-07-16T15:14:27.860Z] Data is 3538469
[debug] [2025-07-16T15:14:27.860Z] [unzip] Entry: client/ (compressed_size=0 bytes, uncompressed_size=0 bytes)
[debug] [2025-07-16T15:14:27.861Z] [unzip] Processing entry: client\
[debug] [2025-07-16T15:14:27.861Z] [unzip] mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\
[debug] [2025-07-16T15:14:27.863Z] [unzip] Entry: client/favicon-16x16.png (compressed_size=293 bytes, uncompressed_size=293 bytes)
[debug] [2025-07-16T15:14:27.863Z] [unzip] Processing entry: client\favicon-16x16.png
[debug] [2025-07-16T15:14:27.863Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client
[debug] [2025-07-16T15:14:27.863Z] [unzip] Writing file: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\favicon-16x16.png
[debug] [2025-07-16T15:14:27.865Z] [unzip] Entry: client/safari-pinned-tab.svg (compressed_size=1433 bytes, uncompressed_size=2611 bytes)
[debug] [2025-07-16T15:14:27.866Z] [unzip] Processing entry: client\safari-pinned-tab.svg
[debug] [2025-07-16T15:14:27.866Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client
[debug] [2025-07-16T15:14:27.866Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\safari-pinned-tab.svg
[debug] [2025-07-16T15:14:27.872Z] [unzip] Entry: client/favicon.ico (compressed_size=2933 bytes, uncompressed_size=13294 bytes)
[debug] [2025-07-16T15:14:27.872Z] [unzip] Processing entry: client\favicon.ico
[debug] [2025-07-16T15:14:27.872Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client
[debug] [2025-07-16T15:14:27.872Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\favicon.ico
[debug] [2025-07-16T15:14:27.875Z] [unzip] Entry: client/index.html (compressed_size=1364 bytes, uncompressed_size=3071 bytes)
[debug] [2025-07-16T15:14:27.875Z] [unzip] Processing entry: client\index.html
[debug] [2025-07-16T15:14:27.875Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client
[debug] [2025-07-16T15:14:27.875Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\index.html
[debug] [2025-07-16T15:14:27.877Z] [unzip] Entry: client/android-chrome-192x192.png (compressed_size=2684 bytes, uncompressed_size=2684 bytes)
[debug] [2025-07-16T15:14:27.877Z] [unzip] Processing entry: client\android-chrome-192x192.png
[debug] [2025-07-16T15:14:27.877Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client
[debug] [2025-07-16T15:14:27.878Z] [unzip] Writing file: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\android-chrome-192x192.png
[debug] [2025-07-16T15:14:27.878Z] [unzip] Entry: client/apple-touch-icon.png (compressed_size=2123 bytes, uncompressed_size=2152 bytes)
[debug] [2025-07-16T15:14:27.879Z] [unzip] Processing entry: client\apple-touch-icon.png
[debug] [2025-07-16T15:14:27.879Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client
[debug] [2025-07-16T15:14:27.879Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\apple-touch-icon.png
[debug] [2025-07-16T15:14:27.881Z] [unzip] Entry: client/android-chrome-512x512.png (compressed_size=5369 bytes, uncompressed_size=5411 bytes)
[debug] [2025-07-16T15:14:27.881Z] [unzip] Processing entry: client\android-chrome-512x512.png
[debug] [2025-07-16T15:14:27.881Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client
[debug] [2025-07-16T15:14:27.882Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\android-chrome-512x512.png
[debug] [2025-07-16T15:14:27.884Z] [unzip] Entry: client/manifest.json (compressed_size=245 bytes, uncompressed_size=551 bytes)
[debug] [2025-07-16T15:14:27.884Z] [unzip] Processing entry: client\manifest.json
[debug] [2025-07-16T15:14:27.884Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client
[debug] [2025-07-16T15:14:27.885Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\manifest.json
[debug] [2025-07-16T15:14:27.889Z] [unzip] Entry: client/robots.txt (compressed_size=26 bytes, uncompressed_size=26 bytes)
[debug] [2025-07-16T15:14:27.890Z] [unzip] Processing entry: client\robots.txt
[debug] [2025-07-16T15:14:27.890Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client
[debug] [2025-07-16T15:14:27.890Z] [unzip] Writing file: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\robots.txt
[debug] [2025-07-16T15:14:27.892Z] [unzip] Entry: client/mstile-150x150.png (compressed_size=2034 bytes, uncompressed_size=2034 bytes)
[debug] [2025-07-16T15:14:27.892Z] [unzip] Processing entry: client\mstile-150x150.png
[debug] [2025-07-16T15:14:27.892Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client
[debug] [2025-07-16T15:14:27.893Z] [unzip] Writing file: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\mstile-150x150.png
[debug] [2025-07-16T15:14:27.895Z] [unzip] Entry: client/assets/ (compressed_size=0 bytes, uncompressed_size=0 bytes)
[debug] [2025-07-16T15:14:27.895Z] [unzip] Processing entry: client\assets\
[debug] [2025-07-16T15:14:27.895Z] [unzip] mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\
[debug] [2025-07-16T15:14:27.896Z] [unzip] Entry: client/assets/index-BX-A56oj.js (compressed_size=582089 bytes, uncompressed_size=2092775 bytes)
[debug] [2025-07-16T15:14:27.896Z] [unzip] Processing entry: client\assets\index-BX-A56oj.js
[debug] [2025-07-16T15:14:27.896Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets
[debug] [2025-07-16T15:14:27.897Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\index-BX-A56oj.js
[debug] [2025-07-16T15:14:27.919Z] [unzip] Entry: client/assets/index-CjB9C900.css (compressed_size=35979 bytes, uncompressed_size=298654 bytes)
[debug] [2025-07-16T15:14:27.919Z] [unzip] Processing entry: client\assets\index-CjB9C900.css
[debug] [2025-07-16T15:14:27.919Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets
[debug] [2025-07-16T15:14:27.919Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\index-CjB9C900.css
[debug] [2025-07-16T15:14:27.922Z] [unzip] Entry: client/assets/index-BX-A56oj.js.map (compressed_size=2362482 bytes, uncompressed_size=10036679 bytes)
[debug] [2025-07-16T15:14:27.922Z] [unzip] Processing entry: client\assets\index-BX-A56oj.js.map
[debug] [2025-07-16T15:14:27.922Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets
[debug] [2025-07-16T15:14:27.922Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\index-BX-A56oj.js.map
[debug] [2025-07-16T15:14:27.969Z] [unzip] Entry: client/assets/extensions/ (compressed_size=0 bytes, uncompressed_size=0 bytes)
[debug] [2025-07-16T15:14:27.969Z] [unzip] Processing entry: client\assets\extensions\
[debug] [2025-07-16T15:14:27.969Z] [unzip] mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\extensions\
[debug] [2025-07-16T15:14:27.969Z] [unzip] Entry: client/assets/extensions/default-extension.png (compressed_size=1646 bytes, uncompressed_size=1657 bytes)
[debug] [2025-07-16T15:14:27.969Z] [unzip] Processing entry: client\assets\extensions\default-extension.png
[debug] [2025-07-16T15:14:27.969Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\extensions
[debug] [2025-07-16T15:14:27.970Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\extensions\default-extension.png
[debug] [2025-07-16T15:14:27.971Z] [unzip] Entry: client/assets/img/ (compressed_size=0 bytes, uncompressed_size=0 bytes)
[debug] [2025-07-16T15:14:27.971Z] [unzip] Processing entry: client\assets\img\
[debug] [2025-07-16T15:14:27.971Z] [unzip] mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\img\
[debug] [2025-07-16T15:14:27.971Z] [unzip] Entry: client/assets/img/database.png (compressed_size=29449 bytes, uncompressed_size=29988 bytes)
[debug] [2025-07-16T15:14:27.971Z] [unzip] Processing entry: client\assets\img\database.png
[debug] [2025-07-16T15:14:27.971Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\img
[debug] [2025-07-16T15:14:27.972Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\img\database.png
[debug] [2025-07-16T15:14:27.973Z] [unzip] Entry: client/assets/provider-icons/ (compressed_size=0 bytes, uncompressed_size=0 bytes)
[debug] [2025-07-16T15:14:27.973Z] [unzip] Processing entry: client\assets\provider-icons\
[debug] [2025-07-16T15:14:27.973Z] [unzip] mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons\
[debug] [2025-07-16T15:14:27.973Z] [unzip] Entry: client/assets/provider-icons/auth_service_saml.svg (compressed_size=575 bytes, uncompressed_size=1226 bytes)
[debug] [2025-07-16T15:14:27.973Z] [unzip] Processing entry: client\assets\provider-icons\auth_service_saml.svg
[debug] [2025-07-16T15:14:27.973Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons
[debug] [2025-07-16T15:14:27.973Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons\auth_service_saml.svg
[debug] [2025-07-16T15:14:27.974Z] [unzip] Entry: client/assets/provider-icons/auth_service_phone.svg (compressed_size=261 bytes, uncompressed_size=414 bytes)
[debug] [2025-07-16T15:14:27.974Z] [unzip] Processing entry: client\assets\provider-icons\auth_service_phone.svg
[debug] [2025-07-16T15:14:27.975Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons
[debug] [2025-07-16T15:14:27.975Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons\auth_service_phone.svg
[debug] [2025-07-16T15:14:27.976Z] [unzip] Entry: client/assets/provider-icons/auth_service_facebook.svg (compressed_size=289 bytes, uncompressed_size=457 bytes)
[debug] [2025-07-16T15:14:27.976Z] [unzip] Processing entry: client\assets\provider-icons\auth_service_facebook.svg
[debug] [2025-07-16T15:14:27.976Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons
[debug] [2025-07-16T15:14:27.976Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons\auth_service_facebook.svg
[debug] [2025-07-16T15:14:27.978Z] [unzip] Entry: client/assets/provider-icons/auth_service_game_center.svg (compressed_size=991 bytes, uncompressed_size=3921 bytes)
[debug] [2025-07-16T15:14:27.978Z] [unzip] Processing entry: client\assets\provider-icons\auth_service_game_center.svg
[debug] [2025-07-16T15:14:27.978Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons
[debug] [2025-07-16T15:14:27.979Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons\auth_service_game_center.svg
[debug] [2025-07-16T15:14:27.980Z] [unzip] Entry: client/assets/provider-icons/auth_service_apple.svg (compressed_size=230 bytes, uncompressed_size=334 bytes)
[debug] [2025-07-16T15:14:27.981Z] [unzip] Processing entry: client\assets\provider-icons\auth_service_apple.svg
[debug] [2025-07-16T15:14:27.981Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons
[debug] [2025-07-16T15:14:27.981Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons\auth_service_apple.svg
[debug] [2025-07-16T15:14:27.983Z] [unzip] Entry: client/assets/provider-icons/auth_service_github.svg (compressed_size=466 bytes, uncompressed_size=838 bytes)
[debug] [2025-07-16T15:14:27.983Z] [unzip] Processing entry: client\assets\provider-icons\auth_service_github.svg
[debug] [2025-07-16T15:14:27.983Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons
[debug] [2025-07-16T15:14:27.983Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons\auth_service_github.svg
[debug] [2025-07-16T15:14:27.985Z] [unzip] Entry: client/assets/provider-icons/auth_service_mslive.svg (compressed_size=203 bytes, uncompressed_size=378 bytes)
[debug] [2025-07-16T15:14:27.986Z] [unzip] Processing entry: client\assets\provider-icons\auth_service_mslive.svg
[debug] [2025-07-16T15:14:27.986Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons
[debug] [2025-07-16T15:14:27.986Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons\auth_service_mslive.svg
[debug] [2025-07-16T15:14:27.988Z] [unzip] Entry: client/assets/provider-icons/auth_service_yahoo.svg (compressed_size=577 bytes, uncompressed_size=1182 bytes)
[debug] [2025-07-16T15:14:27.988Z] [unzip] Processing entry: client\assets\provider-icons\auth_service_yahoo.svg
[debug] [2025-07-16T15:14:27.988Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons
[debug] [2025-07-16T15:14:27.988Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons\auth_service_yahoo.svg
[debug] [2025-07-16T15:14:27.991Z] [unzip] Entry: client/assets/provider-icons/auth_service_twitter.svg (compressed_size=444 bytes, uncompressed_size=751 bytes)
[debug] [2025-07-16T15:14:27.991Z] [unzip] Processing entry: client\assets\provider-icons\auth_service_twitter.svg
[debug] [2025-07-16T15:14:27.991Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons
[debug] [2025-07-16T15:14:27.992Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons\auth_service_twitter.svg
[debug] [2025-07-16T15:14:27.994Z] [unzip] Entry: client/assets/provider-icons/auth_service_play_games.svg (compressed_size=565 bytes, uncompressed_size=1173 bytes)
[debug] [2025-07-16T15:14:27.994Z] [unzip] Processing entry: client\assets\provider-icons\auth_service_play_games.svg
[debug] [2025-07-16T15:14:27.994Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons
[debug] [2025-07-16T15:14:27.995Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons\auth_service_play_games.svg
[debug] [2025-07-16T15:14:27.997Z] [unzip] Entry: client/assets/provider-icons/auth_service_email.svg (compressed_size=228 bytes, uncompressed_size=326 bytes)
[debug] [2025-07-16T15:14:27.998Z] [unzip] Processing entry: client\assets\provider-icons\auth_service_email.svg
[debug] [2025-07-16T15:14:27.998Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons
[debug] [2025-07-16T15:14:27.998Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons\auth_service_email.svg
[debug] [2025-07-16T15:14:27.999Z] [unzip] Entry: client/assets/provider-icons/auth_service_google.svg (compressed_size=409 bytes, uncompressed_size=720 bytes)
[debug] [2025-07-16T15:14:27.999Z] [unzip] Processing entry: client\assets\provider-icons\auth_service_google.svg
[debug] [2025-07-16T15:14:27.999Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons
[debug] [2025-07-16T15:14:28.000Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons\auth_service_google.svg
[debug] [2025-07-16T15:14:28.001Z] [unzip] Entry: client/assets/provider-icons/auth_service_oidc.svg (compressed_size=414 bytes, uncompressed_size=858 bytes)
[debug] [2025-07-16T15:14:28.001Z] [unzip] Processing entry: client\assets\provider-icons\auth_service_oidc.svg
[debug] [2025-07-16T15:14:28.001Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons
[debug] [2025-07-16T15:14:28.002Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\assets\provider-icons\auth_service_oidc.svg
[debug] [2025-07-16T15:14:28.004Z] [unzip] Entry: client/browserconfig.xml (compressed_size=491 bytes, uncompressed_size=822 bytes)
[debug] [2025-07-16T15:14:28.004Z] [unzip] Processing entry: client\browserconfig.xml
[debug] [2025-07-16T15:14:28.004Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client
[debug] [2025-07-16T15:14:28.005Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\browserconfig.xml
[debug] [2025-07-16T15:14:28.006Z] [unzip] Entry: client/favicon-32x32.png (compressed_size=475 bytes, uncompressed_size=475 bytes)
[debug] [2025-07-16T15:14:28.007Z] [unzip] Processing entry: client\favicon-32x32.png
[debug] [2025-07-16T15:14:28.007Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client
[debug] [2025-07-16T15:14:28.007Z] [unzip] Writing file: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\client\favicon-32x32.png
[debug] [2025-07-16T15:14:28.008Z] [unzip] Entry: server/ (compressed_size=0 bytes, uncompressed_size=0 bytes)
[debug] [2025-07-16T15:14:28.008Z] [unzip] Processing entry: server\
[debug] [2025-07-16T15:14:28.008Z] [unzip] mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\server\
[debug] [2025-07-16T15:14:28.009Z] [unzip] Entry: server/server.mjs.map (compressed_size=217845 bytes, uncompressed_size=944978 bytes)
[debug] [2025-07-16T15:14:28.009Z] [unzip] Processing entry: server\server.mjs.map
[debug] [2025-07-16T15:14:28.009Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\server
[debug] [2025-07-16T15:14:28.009Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\server\server.mjs.map
[debug] [2025-07-16T15:14:28.019Z] [unzip] Entry: server/server.mjs (compressed_size=276407 bytes, uncompressed_size=933701 bytes)
[debug] [2025-07-16T15:14:28.019Z] [unzip] Processing entry: server\server.mjs
[debug] [2025-07-16T15:14:28.019Z] [unzip] else mkdir: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\server
[debug] [2025-07-16T15:14:28.020Z] [unzip] deflating: C:\Users\<USER>\.cache\firebase\emulators\ui-v1.15.0\server\server.mjs
[info] i  functions: Watching "C:\DevTools\FlutterApp\daily_spanish_back\functions" for Cloud Functions... {"metadata":{"emulator":{"name":"functions"},"message":"Watching \"C:\\DevTools\\FlutterApp\\daily_spanish_back\\functions\" for Cloud Functions..."}}
[debug] [2025-07-16T15:14:28.036Z] Validating nodejs source
[debug] [2025-07-16T15:14:29.230Z] > [functions] package.json contents: {
  "name": "functions",
  "scripts": {
    "build": "tsc",
    "build:watch": "tsc --watch",
    "serve": "npm run build && firebase emulators:start --only functions",
    "shell": "npm run build && firebase functions:shell",
    "start": "npm run shell",
    "deploy": "firebase deploy --only functions",
    "logs": "firebase functions:log"
  },
  "engines": {
    "node": "22"
  },
  "main": "lib/index.js",
  "dependencies": {
    "@types/cors": "^2.8.19",
    "axios": "^1.10.0",
    "cors": "^2.8.5",
    "dotenv": "^17.2.0",
    "firebase-admin": "^12.6.0",
    "firebase-functions": "^6.0.1",
    "openai": "^5.9.2"
  },
  "devDependencies": {
    "firebase-functions-test": "^3.1.0",
    "typescript": "^5.7.3"
  },
  "private": true
}
[debug] [2025-07-16T15:14:29.230Z] Building nodejs source
[debug] [2025-07-16T15:14:29.231Z] Failed to find version of module node: reached end of search path C:\DevTools\FlutterApp\daily_spanish_back\functions\node_modules
[info] +  functions: Using node@22 from host. 
[debug] [2025-07-16T15:14:29.233Z] Could not find functions.yaml. Must use http discovery
[debug] [2025-07-16T15:14:29.239Z] Found firebase-functions binary at 'C:\DevTools\FlutterApp\daily_spanish_back\functions\node_modules\.bin\firebase-functions'
[info] Serving at port 8580

[error] {"severity":"ERROR","message":"Error: Missing required Firebase Functions config: openai.api_key, youtube.api_key\n    at entryFromArgs (C:\\DevTools\\FlutterApp\\daily_spanish_back\\functions\\node_modules\\firebase-functions\\lib\\logger\\index.js:134:19)\n    at Object.error (C:\\DevTools\\FlutterApp\\daily_spanish_back\\functions\\node_modules\\firebase-functions\\lib\\logger\\index.js:121:11)\n    at Environment.validateEnvironmentVariables (C:\\DevTools\\FlutterApp\\daily_spanish_back\\functions\\lib\\config\\environment.js:97:20)\n    at new Environment (C:\\DevTools\\FlutterApp\\daily_spanish_back\\functions\\lib\\config\\environment.js:44:14)\n    at Environment.getInstance (C:\\DevTools\\FlutterApp\\daily_spanish_back\\functions\\lib\\config\\environment.js:48:36)\n    at Object.<anonymous> (C:\\DevTools\\FlutterApp\\daily_spanish_back\\functions\\lib\\config\\environment.js:117:35)\n    at Module._compile (node:internal/modules/cjs/loader:1730:14)\n    at Object..js (node:internal/modules/cjs/loader:1895:10)\n    at Module.load (node:internal/modules/cjs/loader:1465:32)\n    at Function._load (node:internal/modules/cjs/loader:1282:12)"}

[error] Error: Missing required Firebase Functions config: openai.api_key, youtube.api_key
    at Environment.validateEnvironmentVariables (C:\DevTools\FlutterApp\daily_spanish_back\functions\lib\config\environment.js:98:19)
    at new Environment (C:\DevTools\FlutterApp\daily_spanish_back\functions\lib\config\environment.js:44:14)
    at Environment.getInstance (C:\DevTools\FlutterApp\daily_spanish_back\functions\lib\config\environment.js:48:36)
    at Object.<anonymous> (C:\DevTools\FlutterApp\daily_spanish_back\functions\lib\config\environment.js:117:35)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)

[debug] [2025-07-16T15:14:32.933Z] Got response code 400; body Failed to generate manifest from function source: Missing required Firebase Functions config: openai.api_key, youtube.api_key
