/**
 * Test script to verify environment configuration and API connections
 */

import { environment } from "../config/environment";
import { geminiService } from "../services/gemini.service";
import { youtubeService } from "../services/youtube.service";

async function testEnvironment() {
  console.log("🔧 Testing Environment Configuration...\n");

  try {
    // Test environment variables
    console.log("1. Testing Environment Variables:");
    console.log(
      "   ✅ Gemini API Key:",
      environment.getGeminiApiKey() ? "Configured" : "Missing"
    );
    console.log(
      "   ✅ YouTube API Key:",
      environment.getYouTubeApiKey() ? "Configured" : "Missing"
    );
    console.log(
      "   ✅ Environment Mode:",
      environment.isDevelopment() ? "Development" : "Production"
    );
    console.log("");

    // Test Gemini AI Service
    console.log("2. Testing Gemini AI Service:");
    try {
      const sentence = await geminiService.generateDailySentence();
      console.log("   ✅ Gemini AI Connection: Success");
      console.log("   📝 Generated Sentence:", sentence.sentence);
      console.log("   🇰🇷 Translation:", sentence.translation);
      console.log("");

      // Test YouTube keyword generation
      console.log("3. Testing YouTube Keyword Generation:");
      const keywords = await geminiService.generateYouTubeSearchKeywords(
        sentence.sentence
      );
      console.log("   ✅ Keywords Generated:", keywords);
      console.log("");

      // Test YouTube Service
      console.log("4. Testing YouTube Service:");
      const videos = await youtubeService.searchVideos(keywords);
      console.log("   ✅ YouTube Search: Success");
      console.log("   📺 Videos Found:", videos.length);

      if (videos.length > 0) {
        const bestVideo = youtubeService.selectBestVideo(
          videos,
          sentence.sentence
        );
        if (bestVideo) {
          console.log("   🎯 Best Video:", bestVideo.title);
          console.log("   🔗 URL:", bestVideo.videoUrl);
        }
      }
      console.log("");
    } catch (error) {
      console.error("   ❌ Gemini AI Service Error:", error);
    }

    console.log("✅ Environment test completed successfully!");
  } catch (error) {
    console.error("❌ Environment test failed:", error);
    process.exit(1);
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testEnvironment().catch(console.error);
}

export { testEnvironment };
