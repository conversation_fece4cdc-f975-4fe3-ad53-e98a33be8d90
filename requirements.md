# 하루 한 문장 앱 자동화 요구사항

## 1. AI 자동 문장 생성 및 DB 저장

- 매일 0시에 AI API를 사용하여 오늘의 스페인어 문장과 해당 상황에 어울리는 추가 대화/상황극 문장을 자동 생성한다. (flutter에서 TTS를 이용해 상황극을 할 예정)
- 각 문장에 대한 한글 번역도 함께 생성한다.

- date: 날짜 (YYYY-MM-DD)
- sentence: 오늘의 스페인어 문장
- translation: 한글 번역
- situation: 상황극 스페인어 문장 (예: 실제 대화 형태, 여러 줄 가능)
- situationTranslation: 상황극 한글 번역

## 2. 유튜브 영상 검색 및 URL 저장 - 전체삭제

- AI가 생성한 문장과 의미가 부합하는 유튜브 영상을 YouTube Data API로 검색한다.
- 적합한 영상의 URL(및 썸네일 URL 등)을 Firestore에 문장 데이터와 함께 저장한다.
  - `videoUrl` : 유튜브 영상 링크
  - `videoThumbnail` : 유튜브 썸네일 이미지 링크

## 3. Flutter 앱 연동 및 화면 구현 (추후 flutter에서 진행 해당 프로젝트에서는 필요없음)

- Flutter 앱에서 Firebase Firestore와 연동하여 데이터를 불러온다.
- Firestore에 저장된 오늘의 문장/번역/유튜브 영상/썸네일을 앱 화면에 표시한다.
- 추후 확장시 즐겨찾기, 공유, 발음 듣기 등 부가 기능도 포함할 수 있다.

## 4. API 관리

- 안전을 위해 Firebase 환경변수(Functions Config)를 이용해 CLI에서 키를 등록해서 사용한다.
- .env 파일에 open ai, youtube data api 키를 이용해 등록하고 이후 삭제한다.

## 5. 권장 DB 스키마 구성

- 최소
  {
  "date": "2024-07-19",
  "sentence": "¿Dónde está el baño?",
  "translation": "화장실이 어디에요?",
  "situation": "A: Disculp<PERSON>, ¿dónde está el baño?\nB: El baño está al fondo a la derecha.",
  "situationTranslation": "A: 실례합니다, 화장실이 어디에요?\nB: 화장실은 안쪽 오른편에 있습니다.",
  "imageUrl": "https://images.unsplash.com/xxx" // 선택
  }

---

### [요약]

- **매일 0시 자동화:** AI로 문장 생성 → 유튜브 영상 검색 → Firestore 저장
- **앱 연동:** Flutter에서 Firestore 데이터 불러와 화면에 표시
