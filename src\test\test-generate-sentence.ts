/**
 * Test script to generate and save a daily sentence to Firestore
 */

// Mock Firebase Functions config for local testing
const mockConfig = {
  gemini: { api_key: "AIzaSyCyyWndJbQRD-ktpM7PYPktEzAo_6trjQs" },
  youtube: { api_key: "AIzaSyD8iQgRRyIRrG8ZGzAzgWWiNmQHccqegCg" }
};

// Mock firebase-functions module
const originalModule = require('firebase-functions');
originalModule.config = () => mockConfig;

import { dailySentenceService } from '../services/daily-sentence.service';

async function testGenerateAndSave() {
  console.log('🚀 Testing Daily Sentence Generation and Firestore Save...\n');

  try {
    // Generate today's sentence
    console.log('📝 Generating today\'s sentence...');
    const result = await dailySentenceService.generateTodaysSentence();
    
    console.log('✅ Success! Generated and saved daily sentence:');
    console.log('📅 Date:', result.date);
    console.log('🇪🇸 Spanish:', result.sentence);
    console.log('🇰🇷 Korean:', result.translation);
    
    if (result.videoUrl) {
      console.log('📺 Video URL:', result.videoUrl);
      console.log('🖼️  Thumbnail:', result.videoThumbnail);
    } else {
      console.log('📺 No video found for this sentence');
    }
    
    console.log('\n✅ Daily sentence successfully generated and saved to Firestore!');
    
    // Test retrieval
    console.log('\n🔍 Testing retrieval...');
    const retrieved = await dailySentenceService.getDailySentence();
    
    if (retrieved) {
      console.log('✅ Successfully retrieved from Firestore:');
      console.log('📅 Date:', retrieved.date);
      console.log('🇪🇸 Spanish:', retrieved.sentence);
      console.log('🇰🇷 Korean:', retrieved.translation);
    } else {
      console.log('❌ Failed to retrieve sentence');
    }

  } catch (error) {
    console.error('❌ Error generating daily sentence:', error);
    process.exit(1);
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testGenerateAndSave().catch(console.error);
}

export { testGenerateAndSave };
