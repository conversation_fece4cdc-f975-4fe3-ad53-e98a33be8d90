/**
 * Test script to verify YouTube API connection
 */

import { youtubeService } from '../services/youtube.service';

async function testYouTube() {
  console.log('📺 Testing YouTube Service...\n');

  try {
    // Test YouTube search with Spanish learning keywords
    const testKeywords = 'español conversación aprender spanish learning';
    console.log('🔍 Searching for videos with keywords:', testKeywords);
    
    const videos = await youtubeService.searchVideos(testKeywords);
    console.log('✅ YouTube Search: Success');
    console.log('📺 Videos Found:', videos.length);
    console.log('');

    if (videos.length > 0) {
      console.log('📋 First 3 videos:');
      videos.slice(0, 3).forEach((video, index) => {
        console.log(`${index + 1}. ${video.title}`);
        console.log(`   Channel: ${video.channelTitle}`);
        console.log(`   URL: ${video.videoUrl}`);
        console.log('');
      });

      // Test best video selection
      const testSentence = '¿Dónde está el baño?';
      const bestVideo = youtubeService.selectBestVideo(videos, testSentence);
      
      if (bestVideo) {
        console.log('🎯 Best Video Selected:');
        console.log('   Title:', bestVideo.title);
        console.log('   Channel:', bestVideo.channelTitle);
        console.log('   URL:', bestVideo.videoUrl);
        console.log('   Thumbnail:', bestVideo.thumbnailUrl);
      } else {
        console.log('❌ No suitable video found');
      }
    }

    console.log('\n✅ YouTube test completed successfully!');

  } catch (error) {
    console.error('❌ YouTube test failed:', error);
    process.exit(1);
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testYouTube().catch(console.error);
}

export { testYouTube };
