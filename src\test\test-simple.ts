/**
 * Simple test script to verify API connections without Firebase Functions dependency
 */

import axios from 'axios';

// Test YouTube API directly
async function testYouTubeAPI() {
  console.log('📺 Testing YouTube API directly...\n');

  try {
    const apiKey = 'AIzaSyD8iQgRRyIRrG8ZGzAzgWWiNmQHccqegCg';
    const searchQuery = 'español conversación aprender spanish learning';
    
    const response = await axios.get('https://www.googleapis.com/youtube/v3/search', {
      params: {
        key: apiKey,
        part: 'snippet',
        q: searchQuery,
        type: 'video',
        maxResults: 5,
        order: 'relevance',
        regionCode: 'KR',
        relevanceLanguage: 'es',
        videoDefinition: 'any',
        videoDuration: 'medium',
        safeSearch: 'strict'
      }
    });

    if (response.data.items && response.data.items.length > 0) {
      console.log('✅ YouTube API: Success');
      console.log('📺 Videos Found:', response.data.items.length);
      console.log('');

      console.log('📋 First 3 videos:');
      response.data.items.slice(0, 3).forEach((item: any, index: number) => {
        console.log(`${index + 1}. ${item.snippet.title}`);
        console.log(`   Channel: ${item.snippet.channelTitle}`);
        console.log(`   URL: https://www.youtube.com/watch?v=${item.id.videoId}`);
        console.log('');
      });
    } else {
      console.log('❌ No videos found');
    }

    console.log('✅ YouTube API test completed successfully!');

  } catch (error: any) {
    console.error('❌ YouTube API test failed:', error.response?.data || error.message);
    process.exit(1);
  }
}

// Test OpenAI API directly (if quota allows)
async function testOpenAIAPI() {
  console.log('🤖 Testing OpenAI API directly...\n');

  try {
    const apiKey = 'AIzaSyCyyWndJbQRD-ktpM7PYPktEzAo_6trjQs';
    
    const response = await axios.post('https://api.openai.com/v1/chat/completions', {
      model: 'gpt-3.5-turbo',
      messages: [
        {
          role: 'system',
          content: 'You are a Spanish language teacher helping Korean students learn practical Spanish phrases.'
        },
        {
          role: 'user',
          content: 'Generate a simple Spanish sentence with Korean translation in JSON format: {"sentence": "Spanish here", "translation": "Korean here"}'
        }
      ],
      temperature: 0.7,
      max_tokens: 100
    }, {
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.data.choices && response.data.choices[0]) {
      console.log('✅ OpenAI API: Success');
      console.log('📝 Response:', response.data.choices[0].message.content);
    } else {
      console.log('❌ No response from OpenAI');
    }

    console.log('✅ OpenAI API test completed successfully!');

  } catch (error: any) {
    console.log('❌ OpenAI API test failed:', error.response?.data?.error?.message || error.message);
    console.log('   This might be due to quota limits or invalid API key');
  }
}

async function runTests() {
  console.log('🧪 Running API Tests...\n');
  
  await testYouTubeAPI();
  console.log('\n' + '='.repeat(50) + '\n');
  await testOpenAIAPI();
}

// Run the tests if this file is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

export { testYouTubeAPI, testOpenAIAPI };
