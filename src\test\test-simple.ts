/**
 * Simple test script to verify API connections without Firebase Functions dependency
 */

import axios from "axios";

// Test YouTube API directly
async function testYouTubeAPI() {
  console.log("📺 Testing YouTube API directly...\n");

  try {
    const apiKey = "AIzaSyD8iQgRRyIRrG8ZGzAzgWWiNmQHccqegCg";
    const searchQuery = "español conversación aprender spanish learning";

    const response = await axios.get(
      "https://www.googleapis.com/youtube/v3/search",
      {
        params: {
          key: apiKey,
          part: "snippet",
          q: searchQuery,
          type: "video",
          maxResults: 5,
          order: "relevance",
          regionCode: "KR",
          relevanceLanguage: "es",
          videoDefinition: "any",
          videoDuration: "medium",
          safeSearch: "strict",
        },
      }
    );

    if (response.data.items && response.data.items.length > 0) {
      console.log("✅ YouTube API: Success");
      console.log("📺 Videos Found:", response.data.items.length);
      console.log("");

      console.log("📋 First 3 videos:");
      response.data.items.slice(0, 3).forEach((item: any, index: number) => {
        console.log(`${index + 1}. ${item.snippet.title}`);
        console.log(`   Channel: ${item.snippet.channelTitle}`);
        console.log(
          `   URL: https://www.youtube.com/watch?v=${item.id.videoId}`
        );
        console.log("");
      });
    } else {
      console.log("❌ No videos found");
    }

    console.log("✅ YouTube API test completed successfully!");
  } catch (error: any) {
    console.error(
      "❌ YouTube API test failed:",
      error.response?.data || error.message
    );
    process.exit(1);
  }
}

// Test Google Gemini AI API directly
async function testGeminiAPI() {
  console.log("🤖 Testing Google Gemini AI API directly...\n");

  try {
    const apiKey = "AIzaSyCyyWndJbQRD-ktpM7PYPktEzAo_6trjQs";

    const prompt = `Generate a useful Spanish sentence for daily conversation learning with Korean translation.

    Please respond in the following JSON format only:
    {
      "sentence": "Spanish sentence here",
      "translation": "Korean translation here"
    }`;

    const response = await axios.post(
      `https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${apiKey}`,
      {
        contents: [
          {
            parts: [
              {
                text: prompt,
              },
            ],
          },
        ],
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    if (response.data.candidates && response.data.candidates[0]) {
      const text = response.data.candidates[0].content.parts[0].text;
      console.log("✅ Gemini AI API: Success");
      console.log("📝 Response:", text);

      // Try to parse JSON
      try {
        const cleanedText = text.trim().replace(/```json\n?|\n?```/g, "");
        const parsed = JSON.parse(cleanedText);
        console.log("📝 Parsed Sentence:", parsed.sentence);
        console.log("🇰🇷 Parsed Translation:", parsed.translation);
      } catch (parseError) {
        console.log("⚠️  Could not parse as JSON, but response received");
      }
    } else {
      console.log("❌ No response from Gemini AI");
    }

    console.log("✅ Gemini AI API test completed successfully!");
  } catch (error: any) {
    console.log(
      "❌ Gemini AI API test failed:",
      error.response?.data?.error?.message || error.message
    );
    console.log("   This might be due to API key issues or quota limits");
  }
}

async function runTests() {
  console.log("🧪 Running API Tests...\n");

  await testYouTubeAPI();
  console.log("\n" + "=".repeat(50) + "\n");
  await testGeminiAPI();
}

// Run the tests if this file is executed directly
if (require.main === module) {
  runTests().catch(console.error);
}

export { testYouTubeAPI, testGeminiAPI };
