import * as admin from "firebase-admin";
import * as logger from "firebase-functions/logger";

// Initialize Firebase Admin if not already initialized
if (!admin.apps.length) {
  admin.initializeApp();
}

/**
 * Daily sentence data structure (updated schema)
 */
export interface DailySentence {
  sentence: string;
  translation: string;
  date: string; // YYYY-MM-DD format
  situation: string; // 상황극 스페인어 문장 (대화 형태)
  situationTranslation: string; // 상황극 한글 번역
  imageUrl?: string; // 선택사항 - Unsplash 등 이미지 URL
}

/**
 * Service for interacting with Firestore database
 */
export class FirestoreService {
  private static instance: FirestoreService;
  private db: admin.firestore.Firestore;
  private readonly COLLECTION_NAME = "daily_sentences";

  private constructor() {
    this.db = admin.firestore();
    // Set database name to 'daily-spanish'
    this.db.settings({
      databaseId: "daily-spanish",
    });
  }

  public static getInstance(): FirestoreService {
    if (!FirestoreService.instance) {
      FirestoreService.instance = new FirestoreService();
    }
    return FirestoreService.instance;
  }

  /**
   * Save daily sentence to Firestore
   */
  public async saveDailySentence(
    sentence: string,
    translation: string,
    date: string,
    situation: string,
    situationTranslation: string,
    imageUrl?: string
  ): Promise<string> {
    try {
      const dailySentenceData: DailySentence = {
        sentence,
        translation,
        date,
        situation,
        situationTranslation,
      };

      // Add image URL if available
      if (imageUrl) {
        dailySentenceData.imageUrl = imageUrl;
      }

      // Use date as document ID for easy retrieval
      const docRef = this.db.collection(this.COLLECTION_NAME).doc(date);
      await docRef.set(dailySentenceData);

      logger.info("Daily sentence saved successfully", {
        date,
        sentence,
        hasSituation: !!situation,
        hasImage: !!imageUrl,
      });

      return docRef.id;
    } catch (error) {
      logger.error("Error saving daily sentence:", error);
      throw new Error(`Failed to save daily sentence: ${error}`);
    }
  }

  /**
   * Get daily sentence by date
   */
  public async getDailySentenceByDate(
    date: string
  ): Promise<DailySentence | null> {
    try {
      const docRef = this.db.collection(this.COLLECTION_NAME).doc(date);
      const doc = await docRef.get();

      if (!doc.exists) {
        logger.info("No daily sentence found for date:", date);
        return null;
      }

      const data = doc.data() as DailySentence;
      logger.info("Retrieved daily sentence for date:", date);
      return data;
    } catch (error) {
      logger.error("Error getting daily sentence:", error);
      throw new Error(`Failed to get daily sentence: ${error}`);
    }
  }

  /**
   * Check if daily sentence exists for a specific date
   */
  public async dailySentenceExists(date: string): Promise<boolean> {
    try {
      const docRef = this.db.collection(this.COLLECTION_NAME).doc(date);
      const doc = await docRef.get();
      return doc.exists;
    } catch (error) {
      logger.error("Error checking daily sentence existence:", error);
      return false;
    }
  }

  /**
   * Get recent daily sentences (for testing/debugging)
   */
  public async getRecentSentences(
    limit: number = 10
  ): Promise<DailySentence[]> {
    try {
      const querySnapshot = await this.db
        .collection(this.COLLECTION_NAME)
        .orderBy("createdAt", "desc")
        .limit(limit)
        .get();

      const sentences: DailySentence[] = [];
      querySnapshot.forEach((doc) => {
        sentences.push(doc.data() as DailySentence);
      });

      logger.info(`Retrieved ${sentences.length} recent sentences`);
      return sentences;
    } catch (error) {
      logger.error("Error getting recent sentences:", error);
      throw new Error(`Failed to get recent sentences: ${error}`);
    }
  }

  /**
   * Update image information for existing sentence
   */
  public async updateImageInfo(date: string, imageUrl: string): Promise<void> {
    try {
      const docRef = this.db.collection(this.COLLECTION_NAME).doc(date);

      await docRef.update({
        imageUrl: imageUrl,
      });

      logger.info("Image information updated for date:", date);
    } catch (error) {
      logger.error("Error updating image info:", error);
      throw new Error(`Failed to update image info: ${error}`);
    }
  }
}

// Export singleton instance
export const firestoreService = FirestoreService.getInstance();
