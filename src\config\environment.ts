import * as dotenv from 'dotenv';
import * as logger from 'firebase-functions/logger';

// Load environment variables from .env file
dotenv.config();

/**
 * Environment configuration for the application
 */
export class Environment {
  private static instance: Environment;
  
  private constructor() {
    this.validateEnvironmentVariables();
  }

  public static getInstance(): Environment {
    if (!Environment.instance) {
      Environment.instance = new Environment();
    }
    return Environment.instance;
  }

  /**
   * Get OpenAI API Key
   */
  public getOpenAIApiKey(): string {
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey) {
      throw new Error('OPENAI_API_KEY is not set in environment variables');
    }
    return apiKey;
  }

  /**
   * Get YouTube Data API Key
   */
  public getYouTubeApiKey(): string {
    const apiKey = process.env.YOUTUBE_API_KEY;
    if (!apiKey) {
      throw new Error('YOUTUBE_API_KEY is not set in environment variables');
    }
    return apiKey;
  }

  /**
   * Get Firebase Project ID (optional, usually auto-detected)
   */
  public getFirebaseProjectId(): string | undefined {
    return process.env.FIREBASE_PROJECT_ID;
  }

  /**
   * Validate that all required environment variables are set
   */
  private validateEnvironmentVariables(): void {
    const requiredVars = ['OPENAI_API_KEY', 'YOUTUBE_API_KEY'];
    const missingVars: string[] = [];

    for (const varName of requiredVars) {
      if (!process.env[varName]) {
        missingVars.push(varName);
      }
    }

    if (missingVars.length > 0) {
      const errorMessage = `Missing required environment variables: ${missingVars.join(', ')}`;
      logger.error(errorMessage);
      throw new Error(errorMessage);
    }

    logger.info('Environment variables validated successfully');
  }

  /**
   * Check if running in development mode
   */
  public isDevelopment(): boolean {
    return process.env.NODE_ENV === 'development' || !process.env.NODE_ENV;
  }

  /**
   * Check if running in production mode
   */
  public isProduction(): boolean {
    return process.env.NODE_ENV === 'production';
  }
}

// Export singleton instance
export const environment = Environment.getInstance();
