/**
 * Environment configuration for the application
 */
export class Environment {
  private static instance: Environment;

  private constructor() {
    // Environment validation will be done when accessing the keys
  }

  public static getInstance(): Environment {
    if (!Environment.instance) {
      Environment.instance = new Environment();
    }
    return Environment.instance;
  }

  /**
   * Get Google Gemini API Key from environment variables
   */
  public getGeminiApiKey(): string {
    const apiKey = process.env.GEMINI_API_KEY;
    if (!apiKey) {
      throw new Error("GEMINI_API_KEY is not set in environment variables");
    }
    return apiKey;
  }

  /**
   * Get Firebase Project ID (optional, usually auto-detected)
   */
  public getFirebaseProjectId(): string | undefined {
    return process.env.FIREBASE_PROJECT_ID;
  }

  /**
   * Check if running in development mode
   */
  public isDevelopment(): boolean {
    return process.env.NODE_ENV === "development" || !process.env.NODE_ENV;
  }

  /**
   * Check if running in production mode
   */
  public isProduction(): boolean {
    return process.env.NODE_ENV === "production";
  }
}

// Export singleton instance
export const environment = Environment.getInstance();
