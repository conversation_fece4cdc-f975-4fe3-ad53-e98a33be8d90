import * as functions from "firebase-functions";
import * as logger from "firebase-functions/logger";

/**
 * Environment configuration for the application
 */
export class Environment {
  private static instance: Environment;

  private constructor() {
    this.validateEnvironmentVariables();
  }

  public static getInstance(): Environment {
    if (!Environment.instance) {
      Environment.instance = new Environment();
    }
    return Environment.instance;
  }

  /**
   * Get OpenAI API Key from Firebase Functions config
   */
  public getOpenAIApiKey(): string {
    const config = functions.config();
    const apiKey = config.openai?.api_key;
    if (!apiKey) {
      throw new Error("openai.api_key is not set in Firebase Functions config");
    }
    return apiKey;
  }

  /**
   * Get YouTube Data API Key from Firebase Functions config
   */
  public getYouTubeApiKey(): string {
    const config = functions.config();
    const apiKey = config.youtube?.api_key;
    if (!apiKey) {
      throw new Error(
        "youtube.api_key is not set in Firebase Functions config"
      );
    }
    return apiKey;
  }

  /**
   * Get Firebase Project ID (optional, usually auto-detected)
   */
  public getFirebaseProjectId(): string | undefined {
    return process.env.FIREBASE_PROJECT_ID;
  }

  /**
   * Validate that all required Firebase Functions config variables are set
   */
  private validateEnvironmentVariables(): void {
    const config = functions.config();
    const missingVars: string[] = [];

    if (!config.openai?.api_key) {
      missingVars.push("openai.api_key");
    }
    if (!config.youtube?.api_key) {
      missingVars.push("youtube.api_key");
    }

    if (missingVars.length > 0) {
      const errorMessage = `Missing required Firebase Functions config: ${missingVars.join(
        ", "
      )}`;
      logger.error(errorMessage);
      throw new Error(errorMessage);
    }

    logger.info("Firebase Functions config validated successfully");
  }

  /**
   * Check if running in development mode
   */
  public isDevelopment(): boolean {
    return process.env.NODE_ENV === "development" || !process.env.NODE_ENV;
  }

  /**
   * Check if running in production mode
   */
  public isProduction(): boolean {
    return process.env.NODE_ENV === "production";
  }
}

// Export singleton instance
export const environment = Environment.getInstance();
